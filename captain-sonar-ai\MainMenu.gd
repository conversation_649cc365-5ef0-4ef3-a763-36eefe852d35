extends Control


func _on_quit_button_pressed() -> void:
	get_tree().quit()


func _on_help_button_pressed() -> void:
	get_tree().change_scene_to_file("res://Help.tscn")


func _on_settings_button_pressed() -> void:
	get_tree().change_scene_to_file("res://settings.tscn")


func _on_ai_config_button_pressed() -> void:
	get_tree().change_scene_to_file("res://ai_config.tscn")


func _on_start_button_pressed() -> void:
	get_tree().change_scene_to_file("res://player_selection.tscn")
