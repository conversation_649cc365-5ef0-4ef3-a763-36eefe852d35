extends Node
class_name AI<PERSON>aptain

# AI Captain Station - Handles submarine movement and tactical decisions
# Based on the C++ Captain AI implementation

signal movement_decision(direction: String)

var ai_manager
var difficulty: AIProfile.Difficulty = AIProfile.Difficulty.NORMAL
var response_time: int = 20
var is_station_done: bool = true
var game_over: bool = false

# Movement tracking (matching C++ variables)
var x_failed: Array[bool] = [false, false]
var y_failed: Array[bool] = [false, false]
var x_or_y: bool = false
var cap_path_plan: Array[int] = [0, 0]  # [y, x]

# Enemy tracking
var enemy_position: Vector2i = Vector2i.ZERO
var enemy_certainty: float = 0.0
var be_in_range_of_user: int = 0

# Tactical state
var wants: Array[bool] = [false, false, false]  # guns, detection, silence
var move_for_want: Array[bool] = [false, false, false, false]  # north, south, west, east

# Missing C++ captain variables
var current_personality_time: int = 0
var user_torpedo_position: Array[int] = [0, 0]
var mines_position: Array = []  # Reference to AI manager's mine positions
var mine_count: int = 0
var active_mine: int = 0

func setup(manager: AIManager, diff: AIProfile.Difficulty, response_ms: int) -> void:
	ai_manager = manager
	difficulty = diff
	response_time = response_ms
	be_in_range_of_user = ai_manager.ai_profile.be_in_range_of_user if ai_manager.ai_profile else 0

	# Initialize references to manager data
	if ai_manager:
		mines_position = ai_manager.mines_position
		mine_count = ai_manager.mine_count
		user_torpedo_position = ai_manager.user_torpedo_position

func is_done() -> bool:
	return is_station_done

func set_game_over(is_over: bool) -> void:
	game_over = is_over

func process_station() -> void:
	if game_over or is_station_done:
		return

	is_station_done = false

	# Process decision immediately
	match difficulty:
		AIProfile.Difficulty.EASY:
			_process_easy_difficulty()
		AIProfile.Difficulty.NORMAL:
			_process_normal_difficulty()
		AIProfile.Difficulty.HARD:
			_process_hard_difficulty()

	# Add response delay after processing to simulate decision time
	await get_tree().create_timer(response_time / 1000.0).timeout

	is_station_done = true

func _process_easy_difficulty() -> void:
	# Easy AI - simple random movement with basic collision avoidance
	var current_pos = ai_manager.get_current_position()
	var direction = _get_easy_movement_direction(current_pos)
	
	if direction != "":
		emit_signal("movement_decision", direction)

func _process_normal_difficulty() -> void:
	# Normal AI - more intelligent movement with enemy tracking
	var current_pos = ai_manager.get_current_position()
	var direction = ""
	
	# If we have good enemy position data, move strategically
	if enemy_certainty >= 0.5:
		direction = _get_tactical_movement_direction(current_pos)
	else:
		# Move based on engineer recommendations or randomly
		direction = _get_engineer_recommended_movement(current_pos)
		if direction == "":
			direction = _get_easy_movement_direction(current_pos)
	
	if direction != "":
		emit_signal("movement_decision", direction)

func _process_hard_difficulty() -> void:
	# Hard AI - advanced tactical movement (placeholder for future implementation)
	_process_normal_difficulty()

func _get_easy_movement_direction(current_pos: Vector2i) -> String:
	# Implements the C++ AIEasyDifficultyPath logic
	var direction = ""
	var new_pos = current_pos
	var attempts = 0
	var max_attempts = 8
	
	while attempts < max_attempts:
		# Try to move in a direction that hasn't failed recently
		if randf() < 0.5:  # Try Y movement
			if not y_failed[0] or not y_failed[1]:
				if randf() < 0.5 and not y_failed[0]:
					direction = "SOUTH"
					new_pos = current_pos + Vector2i(0, 1)
					y_failed[0] = true
				elif not y_failed[1]:
					direction = "NORTH"
					new_pos = current_pos + Vector2i(0, -1)
					y_failed[1] = true
				else:
					direction = "SOUTH"
					new_pos = current_pos + Vector2i(0, 1)
					y_failed[0] = true
		else:  # Try X movement
			if not x_failed[0] or not x_failed[1]:
				if randf() < 0.5 and not x_failed[0]:
					direction = "EAST"
					new_pos = current_pos + Vector2i(1, 0)
					x_failed[0] = true
				elif not x_failed[1]:
					direction = "WEST"
					new_pos = current_pos + Vector2i(-1, 0)
					x_failed[1] = true
				else:
					direction = "EAST"
					new_pos = current_pos + Vector2i(1, 0)
					x_failed[0] = true
		
		# Check if the position is valid
		if ai_manager.is_position_valid(new_pos):
			# Reset failure flags on successful movement
			x_failed = [false, false]
			y_failed = [false, false]
			return direction
		
		attempts += 1
	
	# If all directions failed, reset flags and return empty
	x_failed = [false, false]
	y_failed = [false, false]
	return ""

func _get_tactical_movement_direction(current_pos: Vector2i) -> String:
	# Implements the C++ AINormalDifficultyPath logic for enemy tracking
	var direction = ""
	var target_pos = enemy_position
	var distance_to_enemy = current_pos.distance_to(target_pos)
	
	# If we're within desired range, maintain position or move tactically
	if distance_to_enemy <= be_in_range_of_user + 2:
		# We're close enough, try to maintain tactical position
		return _get_tactical_positioning_move(current_pos, target_pos)
	
	# Move closer to enemy
	var delta = target_pos - current_pos
	var preferred_directions = []
	
	# Prioritize movement directions based on distance to enemy
	if abs(delta.y) > abs(delta.x):
		if delta.y > 0:
			preferred_directions.append("SOUTH")
		else:
			preferred_directions.append("NORTH")
		
		if delta.x > 0:
			preferred_directions.append("EAST")
		else:
			preferred_directions.append("WEST")
	else:
		if delta.x > 0:
			preferred_directions.append("EAST")
		else:
			preferred_directions.append("WEST")
		
		if delta.y > 0:
			preferred_directions.append("SOUTH")
		else:
			preferred_directions.append("NORTH")
	
	# Try preferred directions
	for dir in preferred_directions:
		var new_pos = current_pos + _direction_to_vector(dir)
		if ai_manager.is_position_valid(new_pos):
			return dir
	
	# Fall back to any valid direction
	return _get_easy_movement_direction(current_pos)

func _get_tactical_positioning_move(current_pos: Vector2i, enemy_pos: Vector2i) -> String:
	# Try to position tactically around the enemy
	var possible_moves = ["NORTH", "SOUTH", "EAST", "WEST"]
	var best_direction = ""
	var best_score = -1.0
	
	for direction in possible_moves:
		var new_pos = current_pos + _direction_to_vector(direction)
		if ai_manager.is_position_valid(new_pos):
			var score = _evaluate_tactical_position(new_pos, enemy_pos)
			if score > best_score:
				best_score = score
				best_direction = direction
	
	return best_direction

func _evaluate_tactical_position(pos: Vector2i, enemy_pos: Vector2i) -> float:
	# Evaluate how good a position is tactically
	var distance = pos.distance_to(enemy_pos)
	var score = 0.0
	
	# Prefer positions within weapon range but not too close
	if distance >= 2 and distance <= 4:
		score += 10.0
	elif distance < 2:
		score -= 5.0  # Too close
	elif distance > 6:
		score -= 2.0  # Too far
	
	# Add some randomness for unpredictability
	score += randf() * 2.0
	
	return score

func _get_engineer_recommended_movement(current_pos: Vector2i) -> String:
	# Check if engineer recommends specific movement to keep systems operational
	if move_for_want[0]:  # North
		var new_pos = current_pos + Vector2i(0, -1)
		if ai_manager.is_position_valid(new_pos):
			return "NORTH"
	
	if move_for_want[1]:  # South
		var new_pos = current_pos + Vector2i(0, 1)
		if ai_manager.is_position_valid(new_pos):
			return "SOUTH"
	
	if move_for_want[2]:  # West
		var new_pos = current_pos + Vector2i(-1, 0)
		if ai_manager.is_position_valid(new_pos):
			return "WEST"
	
	if move_for_want[3]:  # East
		var new_pos = current_pos + Vector2i(1, 0)
		if ai_manager.is_position_valid(new_pos):
			return "EAST"
	
	return ""

func _direction_to_vector(direction: String) -> Vector2i:
	match direction:
		"NORTH":
			return Vector2i(0, -1)
		"SOUTH":
			return Vector2i(0, 1)
		"EAST":
			return Vector2i(1, 0)
		"WEST":
			return Vector2i(-1, 0)
		_:
			return Vector2i.ZERO

func update_enemy_position(pos: Vector2i, certainty: float) -> void:
	enemy_position = pos
	enemy_certainty = certainty

func update_wants(guns: bool, detection: bool, silence: bool) -> void:
	wants[0] = guns
	wants[1] = detection
	wants[2] = silence

func update_movement_recommendations(north: bool, south: bool, west: bool, east: bool) -> void:
	move_for_want[0] = north
	move_for_want[1] = south
	move_for_want[2] = west
	move_for_want[3] = east

func start_movement_decision() -> void:
	is_station_done = false
	process_station()

# Captain weapon usage functions (matching C++ implementation)
func easy_cap_use_items(use: int = -1) -> bool:
	# A simple way to check if the AI can do anything
	if use == -1:
		use = randi() % 6

	# If we have saved positions, prioritize torpedo/drone
	if ai_manager and ai_manager.ai_radio_operator and ai_manager.ai_radio_operator.positions_saved[0]:
		use = randi() % 2 + 2  # Focus on torpedo/drone

	match use:
		0:  # Place mine
			if ai_manager and ai_manager.equipment_current.get("mines", 0) >= ai_manager.equipment_max.get("mines", 3):
				if not ai_manager.guns_broke:
					_place_mine_easy()
					return true
		1:  # Activate mine
			if mine_count > 0 and not ai_manager.guns_broke:
				_activate_mine_easy()
				return true
		2:  # Launch torpedo
			if ai_manager and ai_manager.equipment_current.get("torpedo", 0) >= ai_manager.equipment_max.get("torpedo", 4):
				if not ai_manager.guns_broke:
					_launch_torpedo_easy()
					return true
		3:  # Launch drone
			if ai_manager and ai_manager.equipment_current.get("drones", 0) >= ai_manager.equipment_max.get("drones", 3):
				if not ai_manager.detection_broke:
					_launch_drone_easy()
					return true
		4:  # Use sonar
			if ai_manager and ai_manager.equipment_current.get("sonar", 0) >= ai_manager.equipment_max.get("sonar", 3):
				if not ai_manager.detection_broke:
					_use_sonar_easy()
					return true
		5:  # Use silence
			if ai_manager and ai_manager.equipment_current.get("silence", 0) >= ai_manager.equipment_max.get("silence", 6):
				if not ai_manager.special_broke:
					_use_silence_easy()
					return true

	return false

func normal_cap_use_items(use: int = -1) -> bool:
	# More advanced way of checking and using items
	if use == -1:
		use = randi() % 6

	# If we have saved positions, prioritize torpedo/drone
	if ai_manager and ai_manager.ai_radio_operator and ai_manager.ai_radio_operator.positions_saved[0]:
		use = randi() % 2 + 2  # Focus on torpedo/drone

	match use:
		0:  # Place mine strategically
			if ai_manager and ai_manager.equipment_current.get("mines", 0) >= ai_manager.equipment_max.get("mines", 3):
				if not ai_manager.guns_broke:
					_place_mine_normal()
					return true
		1:  # Activate mine with targeting
			if mine_count > 0 and not ai_manager.guns_broke:
				_activate_mine_normal()
				return true
		2:  # Launch torpedo with pathfinding
			if ai_manager and ai_manager.equipment_current.get("torpedo", 0) >= ai_manager.equipment_max.get("torpedo", 4):
				if not ai_manager.guns_broke:
					_launch_torpedo_normal()
					return true
		3:  # Launch drone with sector targeting
			if ai_manager and ai_manager.equipment_current.get("drones", 0) >= ai_manager.equipment_max.get("drones", 3):
				if not ai_manager.detection_broke:
					_launch_drone_normal()
					return true
		4:  # Use sonar strategically
			if ai_manager and ai_manager.equipment_current.get("sonar", 0) >= ai_manager.equipment_max.get("sonar", 3):
				if not ai_manager.detection_broke:
					_use_sonar_normal()
					return true
		5:  # Use silence tactically
			if ai_manager and ai_manager.equipment_current.get("silence", 0) >= ai_manager.equipment_max.get("silence", 6):
				if not ai_manager.special_broke:
					_use_silence_normal()
					return true

	return false

# Weapon implementation functions
func _place_mine_easy() -> void:
	if not ai_manager or mine_count >= 5:
		return

	var current_pos = ai_manager.get_current_position()
	var mine_x = current_pos.x + randi() % 3 - 1  # Random position near AI
	var mine_y = current_pos.y + randi() % 3 - 1

	# Ensure mine position is valid
	if ai_manager.no_no_areas(mine_y, mine_x, false, true):
		mines_position[mine_count] = [mine_x, mine_y]
		mine_count += 1
		ai_manager.mine_count = mine_count
		ai_manager.equipment_current["mines"] = 0  # Reset equipment
		ai_manager.ai_called[0] = true
		print("Captain: I'm doing placed mine at [", mine_x, ", ", mine_y, "]")

func _place_mine_normal() -> void:
	if not ai_manager or mine_count >= 5:
		return

	var current_pos = ai_manager.get_current_position()
	var best_x = current_pos.x
	var best_y = current_pos.y

	# Try to place mine in enemy's likely path
	if enemy_certainty > 0.3:
		# Place mine between AI and estimated enemy position
		var delta_x = enemy_position.x - current_pos.x
		var delta_y = enemy_position.y - current_pos.y
		best_x = current_pos.x + sign(delta_x) * 2
		best_y = current_pos.y + sign(delta_y) * 2

	# Ensure mine position is valid
	if ai_manager.no_no_areas(best_y, best_x, false, true):
		mines_position[mine_count] = [best_x, best_y]
		mine_count += 1
		ai_manager.mine_count = mine_count
		ai_manager.equipment_current["mines"] = 0
		ai_manager.ai_called[0] = true
		print("Captain: I'm doing strategically placed mine at [", best_x, ", ", best_y, "]")

func _activate_mine_easy() -> void:
	if mine_count == 0:
		return

	# Activate random mine
	var mine_to_activate = randi() % mine_count
	var mine_pos = mines_position[mine_to_activate]

	print("Captain: I'm doing activated mine at [", mine_pos[0], ", ", mine_pos[1], "]")
	_remove_mine(mine_to_activate)
	ai_manager.ai_called[6] = true

func _activate_mine_normal() -> void:
	if mine_count == 0:
		return

	# Find best mine to activate based on enemy position
	var best_mine = -1
	var best_distance = 999.0

	for i in range(mine_count):
		var mine_pos = mines_position[i]
		var distance = Vector2i(mine_pos[0], mine_pos[1]).distance_to(enemy_position)
		if distance < best_distance and distance <= 1:  # Mine effective range
			best_distance = distance
			best_mine = i

	if best_mine >= 0:
		var mine_pos = mines_position[best_mine]
		print("Captain: I'm doing strategically activated mine at [", mine_pos[0], ", ", mine_pos[1], "]")
		_remove_mine(best_mine)
		ai_manager.ai_called[6] = true

func _remove_mine(mine_index: int) -> void:
	# Remove mine from array and shift remaining mines
	for i in range(mine_index, mine_count - 1):
		mines_position[i] = mines_position[i + 1]
	mine_count -= 1
	ai_manager.mine_count = mine_count

func _launch_torpedo_easy() -> void:
	if not ai_manager:
		return

	var current_pos = ai_manager.get_current_position()
	user_torpedo_position = [current_pos.x, current_pos.y]
	ai_manager.user_torpedo_position = user_torpedo_position
	ai_manager.equipment_current["torpedo"] = 0
	ai_manager.ai_called[1] = true
	print("Captain: I'm doing launched torpedo from [", current_pos.x, ", ", current_pos.y, "]")

func _launch_torpedo_normal() -> void:
	if not ai_manager:
		return

	var current_pos = ai_manager.get_current_position()

	# Check if torpedo can reach estimated enemy position
	if enemy_certainty > 0.5:
		if ai_manager.torpedo_check(current_pos.y, current_pos.x, enemy_position.y, enemy_position.x):
			user_torpedo_position = [enemy_position.x, enemy_position.y]
			ai_manager.user_torpedo_position = user_torpedo_position
			ai_manager.equipment_current["torpedo"] = 0
			ai_manager.ai_called[1] = true
			print("Captain: I'm doing launched targeted torpedo at [", enemy_position.x, ", ", enemy_position.y, "]")
			return

	# Fallback to easy torpedo
	_launch_torpedo_easy()

func _launch_drone_easy() -> void:
	if not ai_manager:
		return

	ai_manager.equipment_current["drones"] = 0
	ai_manager.ai_called[2] = true
	print("Captain: I'm doing launched drone")

func _launch_drone_normal() -> void:
	if not ai_manager:
		return

	# Target drone at estimated enemy sector
	if enemy_certainty > 0.3:
		var target_sector = ai_manager.sector_positions(enemy_position.y, enemy_position.x)
		print("Captain: I'm doing launched drone targeting sector ", target_sector)
	else:
		print("Captain: I'm doing launched drone for reconnaissance")

	ai_manager.equipment_current["drones"] = 0
	ai_manager.ai_called[2] = true

func _use_sonar_easy() -> void:
	if not ai_manager:
		return

	ai_manager.equipment_current["sonar"] = 0
	ai_manager.ai_called[3] = true
	print("Captain: I'm doing activated sonar")

func _use_sonar_normal() -> void:
	if not ai_manager:
		return

	# Use sonar strategically based on enemy tracking
	if enemy_certainty < 0.5:
		print("Captain: I'm doing used sonar to improve enemy tracking")
	else:
		print("Captain: I'm doing used sonar to confirm enemy position")

	ai_manager.equipment_current["sonar"] = 0
	ai_manager.ai_called[3] = true

func _use_silence_easy() -> void:
	if not ai_manager:
		return

	ai_manager.equipment_current["silence"] = 0
	ai_manager.ai_called[4] = true
	print("Captain: I'm doing activated silence")

func _use_silence_normal() -> void:
	if not ai_manager:
		return

	# Use silence tactically to confuse enemy tracking
	print("Captain: I'm doing used silence to break enemy tracking")
	ai_manager.equipment_current["silence"] = 0
	ai_manager.ai_called[4] = true
