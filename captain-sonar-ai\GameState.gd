extends Node

signal submarine_moved(team_id, direction)
signal state_updated(team_id)
signal ai_action_performed(team_id, action_type, details)

const TEAM_IDS = ["alpha", "bravo"]

# Player role and AI difficulty selections
enum PlayerRole {
	NONE,
	RADIO_OPERATOR,
	CAPTAIN,
	ENGINEER,
	FIRST_MATE
}

enum AIDifficulty {
	NONE,
	EASY,
	MEDIUM,
	HARD
}

var teams: Dictionary = {}
var player_role: PlayerRole = PlayerRole.NONE
var ai_difficulty: AIDifficulty = AIDifficulty.NONE
var map_data: Array = []  # Store map data for collision detection

# New station-based assignment system
var player_assignments: Dictionary = {}
var enemy_assignments: Dictionary = {}
var human_controlled_station: int = 0  # StationType enum value

# AI Management
var ai_manager: AIManager
var ai_enabled: bool = true
var ai_team_id: String = "bravo"  # AI controls the enemy team by default
var ai_turn_in_progress: bool = false  # Prevent multiple AI turns

func _ready() -> void:
	# You'd typically call initialize_game(some_map_data) from your main scene or game orchestrator
	pass

# Initializes a fresh match. This should be called at the start of a game.
func initialize_game(map_data_input: Array) -> void:
	teams.clear()
	map_data = map_data_input  # Store map data for collision detection

	for id in TEAM_IDS:
		teams[id] = SubmarineState.new()

	# Automatically find valid spawn positions for submarines
	var spawn_positions = _find_valid_spawn_positions()

	if teams.has("alpha") and spawn_positions.size() > 0:
		teams["alpha"].position = spawn_positions[0]
		teams["alpha"].heading = "EAST"

	if teams.has("bravo") and spawn_positions.size() > 1:
		teams["bravo"].position = spawn_positions[1]
		teams["bravo"].heading = "WEST"
		teams["bravo"].is_surfaced = false  # Enemy starts submerged

	# Initialize AI if enabled
	if ai_enabled:
		_initialize_ai_manager()

	# Emit initial state updates
	for team_id in TEAM_IDS:
		emit_signal("state_updated", team_id)

# Find valid water positions for submarine spawning
func _find_valid_spawn_positions() -> Array[Vector2i]:
	var valid_positions: Array[Vector2i] = []

	if map_data.size() == 0:
		# Fallback positions if no map data
		return [Vector2i(2, 2), Vector2i(12, 12)]

	# Scan the map for water tiles (avoiding edges for safety)
	var map_width = map_data.size()
	var map_height = map_data[0].size() if map_width > 0 else 0

	for x in range(0, map_width):
		for y in range(0, map_height):
			if not map_data[x][y]:  # false = water, true = island
				valid_positions.append(Vector2i(x, y))

	# If we found valid positions, select two that are far apart
	if valid_positions.size() >= 2:
		# Sort by distance from center to get spread out positions
		var center = Vector2i(map_width / 2, map_height / 2)
		valid_positions.sort_custom(func(a, b): return a.distance_to(center) < b.distance_to(center))

		# Return first (closest to center) and last (farthest from center) for good separation
		return [valid_positions[0], valid_positions[-1]]
	elif valid_positions.size() == 1:
		# Only one valid position found
		return [valid_positions[0], valid_positions[0]]
	else:
		# No valid positions found, use fallback
		print("Warning: No valid spawn positions found, using fallback positions")
		return [Vector2i(2, 2), Vector2i(12, 12)]

# Updates a submarine position and logs the move.
func process_captain_move(team_id: String, direction: String) -> void:
	print("Game State: I'm doing process_captain_move called - team_id: ", team_id, ", direction: ", direction)

	if not teams.has(team_id):
		print("Game State: I'm doing team not found: ", team_id)
		return

	var state: SubmarineState = teams[team_id]
	print("Game State: I'm doing current position: ", state.position)

	var delta := Vector2i.ZERO
	match direction:
		"NORTH":
			delta = Vector2i(0, -1)
		"SOUTH":
			delta = Vector2i(0, 1)
		"EAST":
			delta = Vector2i(1, 0)
		"WEST":
			delta = Vector2i(-1, 0)
		_:
			print("Game State: I'm doing invalid direction: ", direction)
			return

	# Calculate new position
	var new_position = state.position + delta
	# Get map dimensions
	var map_width = map_data.size() if map_data.size() > 0 else 15
	var map_height = map_data[0].size() if map_data.size() > 0 and map_data[0].size() > 0 else 15

	# Debug output only when AI debug mode is enabled
	if ai_manager and ai_manager.debug_mode:
		print("GameState: Move ", state.position, " -> ", new_position, " (", direction, ")")

	# Check boundaries (dynamic based on actual map size)
	if new_position.x < 0 or new_position.x >= map_width or new_position.y < 0 or new_position.y >= map_height:
		if ai_manager and ai_manager.debug_mode:
			print("GameState: Move rejected - out of bounds")
		return

	# Check for collision with islands (if map_data is available)
	if map_data.size() > 0 and new_position.x < map_data.size() and new_position.y < map_data[new_position.x].size():
		if map_data[new_position.x][new_position.y] == true:  # true = island
			if ai_manager and ai_manager.debug_mode:
				print("GameState: Move rejected - island collision")
			return

	# Update position only if valid
	state.position = new_position
	state.heading = direction
	state.move_log.append(direction)
	state.heat += 0.1

	emit_signal("submarine_moved", team_id, direction)
	emit_signal("state_updated", team_id)

	# Notify AI about enemy movement
	if ai_manager and ai_manager.ai_radio_operator and team_id != ai_team_id:
		if ai_manager.debug_mode:
			print("GameState: Notifying AI radio operator about enemy movement")
		ai_manager.ai_radio_operator.track_enemy_movement(direction)
		
	else:
		print("Game State: I'm doing no AI notification - ai_manager: ", ai_manager != null, ", team_id: ", team_id, ", ai_team_id: ", ai_team_id)

# AI Management Functions
func _initialize_ai_manager() -> void:
	if ai_manager:
		ai_manager.queue_free() # Clean up old manager if exists

	ai_manager = AIManager.new()
	ai_manager.team_id = ai_team_id
	ai_manager.debug_mode = true  # Enable debug output
	add_child(ai_manager)

	# Connect AI signals once during initialization
	ai_manager.connect("ai_movement_decision", _on_ai_movement_decision)
	ai_manager.connect("ai_wants_to_surface", _on_ai_wants_to_surface)
	ai_manager.connect("ai_action_completed", _on_ai_action_completed)
	ai_manager.connect("ai_turn_completed", _on_ai_turn_completed)

	if ai_manager.debug_mode:
		print("Game State: AI Manager initialized and signals connected.")

func _on_ai_movement_decision(direction: String) -> void:
	# AI has decided to move - execute the movement
	process_captain_move(ai_team_id, direction)
	print("Game State: AI Captain's movement executed: ", direction)

func _on_ai_wants_to_surface() -> void:
	# AI wants to surface
	if teams.has(ai_team_id):
		var ai_state: SubmarineState = teams[ai_team_id]
		# The AIManager itself handles the 'is_surfaced' toggle and delay.
		# This signal primarily informs GameState for display purposes.
		print("Game State: AI wants to surface event received.")

func _on_ai_action_completed(station_type: AIProfile.StationType, action: String) -> void:
	# AI has completed an action
	emit_signal("ai_action_performed", ai_team_id, station_type, action)
	print("Game State: AI ", AIProfile.StationType.keys()[station_type], " completed: ", action)

func set_ai_enabled(enabled: bool) -> void:
	ai_enabled = enabled
	if not enabled and ai_manager:
		ai_manager.queue_free()
		ai_manager = null
	print("Game State: AI Enabled set to: ", enabled)

func set_ai_difficulty(captain_diff: AIProfile.Difficulty, engineer_diff: AIProfile.Difficulty,
					   first_mate_diff: AIProfile.Difficulty, radio_diff: AIProfile.Difficulty) -> void:
	if ai_manager and ai_manager.ai_profile:
		ai_manager.ai_profile.captain_difficulty = captain_diff
		ai_manager.ai_profile.engineer_difficulty = engineer_diff
		ai_manager.ai_profile.first_mate_difficulty = first_mate_diff
		ai_manager.ai_profile.radio_operator_difficulty = radio_diff
	print("Game State: AI difficulties updated.")

func trigger_ai_turn() -> void:
	# Trigger AI to make decisions (called each turn)
	if ai_manager and ai_manager.debug_mode:
		print("GameState: Starting AI turn")

	# Prevent multiple AI turns from running simultaneously
	if ai_turn_in_progress:
		if ai_manager and ai_manager.debug_mode:
			print("GameState: AI turn already in progress, skipping")
		return

	ai_turn_in_progress = true

	if not ai_manager:
		if ai_manager and ai_manager.debug_mode:
			print("GameState: No AI manager found")
		ai_turn_in_progress = false
		return

	# Signal is already connected during initialization

	# Start AI turn and wait for completion signal
	ai_manager.start_ai_turn()
	
func _on_ai_turn_completed() -> void:
	"""Called when AI Manager signals that all stations have completed their turn"""
	if ai_manager and ai_manager.debug_mode:
		print("GameState: AI turn completed")
	ai_turn_in_progress = false

func get_ai_status() -> Dictionary:
	if not ai_manager:
		return {}

	return {
		"enabled": ai_enabled,
		"team_id": ai_team_id,
		"health": ai_manager.ai_health,
		"surfaced": ai_manager.ai_surfaced,
		"equipment": ai_manager.equipment_current.duplicate(),
		"radio_certainty": ai_manager.ai_radio_operator.get_certainty() if ai_manager.ai_radio_operator else 0.0
	}
