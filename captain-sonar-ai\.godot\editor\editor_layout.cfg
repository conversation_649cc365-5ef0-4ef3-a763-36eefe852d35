[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=240
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=-210
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://main_menu.tscn", "res://game.tscn", "res://settings.tscn", "res://help.tscn", "res://player.tscn", "res://EnemyAI.tscn", "res://weapons_station.tscn", "res://engineer_station.tscn", "res://radio_station.tscn", "res://bridge.tscn", "res://PathOverlay.tscn", "res://SpectatorView.tscn", "res://player_selection.tscn")
current_scene="res://engineer_station.tscn"
center_split_offset=-301
selected_default_debugger_tab_idx=0
selected_main_editor_idx=2
selected_bottom_panel_item=1

[EditorWindow]

screen=0
mode="maximized"
position=Vector2i(0, 29)

[ScriptEditor]

open_scripts=["res://AICaptain.gd", "res://AIEngineer.gd", "res://AIFirstMate.gd", "res://AIManager.gd", "res://AIPerformanceMonitor.gd", "res://AIProfile.gd", "res://AIRadioOperator.gd", "res://DraggableOverlay.gd", "res://game.gd", "res://GameState.gd", "res://help.gd", "res://MainMenu.gd", "res://path_overlay.gd", "res://PlayerSelection.gd", "res://PLAYER_SELECTION_README.md", "res://radio_station.gd", "res://settings.gd", "res://SpectatorView.gd", "res://SubmarineState.gd"]
selected_script="res://AIPerformanceMonitor.gd"
open_help=["@GlobalScope", "TextEdit", "VBoxContainer"]
script_split_offset=200
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(378, 150, 1164, 695)
floating_window_screen=0

[ShaderEditor]

open_shaders=[]
split_offset=200
selected_shader=""
text_shader_zoom_factor=1.0

[editor_log]

log_filter_0=true
log_filter_2=true
log_filter_1=true
log_filter_3=true
log_filter_4=true
collapse=false
show_search=true
