extends Node
class_name AIRadioOperator

# AI Radio Operator Station - Tracks enemy movements and estimates positions
# Fixes: Infinite loops, processing flags, bounded iterations

signal enemy_position_updated(estimated_position: Vector2i, certainty: float)
signal enemy_surfaced()
signal intelligence_gathered(info_type: String, data: Dictionary)

var ai_manager
var difficulty: AIProfile.Difficulty = AIProfile.Difficulty.NORMAL
var response_time: int = 20
var is_station_done: bool = true
var game_over: bool = false
var is_processing: bool = false  # Prevents overlapping processing calls

# Enemy movement tracking
var user_positions: Array[String] = []  # Log of enemy movements
var user_position_count: int = 0
var user_read_tries: int = 0
var radio_certainty: int = 0

# Position tracking arrays
var tried_position: Array = []  # Array of Vector2i positions we've tried
var user_dead_zones: Array = []  # 15x15 bool array - where enemy has been
var positions_tried: Array = []  # 15x15 bool array - positions we've checked

# Sector analysis for normal+ difficulty
var user_compass_counts: Array[int] = [0, 0, 0, 0]  # N, S, E, W movement counts
var user_sector_starters: Array[bool] = []  # Which sectors (1-9) we've eliminated
var sector_trying: int = 10  # Current sector being checked (10 = none)
var y_s_of_sector_check: int = 0  # Y offset within current sector
var x_s_of_sector_check: int = 0  # X offset within current sector

func setup(manager: AIManager, diff: AIProfile.Difficulty, response_ms: int) -> void:
	ai_manager = manager
	difficulty = diff
	response_time = response_ms
	_initialize_tracking_arrays()
	print("Radio Operator: Setup complete - Difficulty: ", difficulty)

func _initialize_tracking_arrays() -> void:
	"""Initialize all tracking arrays to proper sizes"""
	user_dead_zones.clear()
	positions_tried.clear()
	user_sector_starters.clear()
	
	# Create 15x15 grids
	for x in range(15):
		user_dead_zones.append([])
		positions_tried.append([])
		for y in range(15):
			user_dead_zones[x].append(false)
			positions_tried[x].append(false)
	
	# Initialize 9 sectors (1-9) as not eliminated
	for i in range(9):
		user_sector_starters.append(false)
	
	# Initialize position history
	tried_position.clear()
	for i in range(51):  # Match original array size
		tried_position.append(Vector2i.ZERO)

func is_done() -> bool:
	return is_station_done

func process_station() -> void:
	"""Main processing function - called each AI turn"""
	# Prevent overlapping calls
	if is_processing or game_over:
		return

	is_processing = true
	is_station_done = false

	if ai_manager and ai_manager.debug_mode:
		print("Radio Operator: Starting analysis (Difficulty: ", difficulty, ")")

	# Process immediately, then add realistic response delay
	match difficulty:
		AIProfile.Difficulty.EASY:
			_process_easy_difficulty()
		AIProfile.Difficulty.NORMAL:
			_process_normal_difficulty()
		AIProfile.Difficulty.HARD:
			_process_hard_difficulty()

	# Add response delay after processing to simulate thinking time
	var delay_time = (randf() * 0.3 + response_time) / 1000.0
	await get_tree().create_timer(delay_time).timeout

	# Mark as complete and reset processing flag
	is_station_done = true
	is_processing = false

	if ai_manager and ai_manager.debug_mode:
		print("Radio Operator: Analysis complete")

func _process_easy_difficulty() -> void:
	"""Easy mode - simple random position guessing"""
	print("Radio Operator: Using basic tracking")
	
	if user_positions.size() == 0:
		print("Radio Operator: No enemy movements to track yet")
		return
	
	# Pick a random valid position as enemy estimate
	var attempts = 0
	var found_position = false
	var estimated_pos = Vector2i.ZERO
	
	while not found_position and attempts < 20:
		estimated_pos = Vector2i(randi() % 15, randi() % 15)
		if _is_position_valid_for_enemy(estimated_pos):
			found_position = true
		attempts += 1
	
	if found_position:
		tried_position[0] = estimated_pos
		user_dead_zones[estimated_pos.y][estimated_pos.x] = true
		
		# Low certainty for easy mode
		var certainty = min(0.3 + randf() * 0.2, 1.0)
		emit_signal("enemy_position_updated", estimated_pos, certainty)
		print("Radio Operator: Enemy estimated at [", estimated_pos.x, ", ", estimated_pos.y, 
			  "] (certainty: ", certainty, ")")

func _process_normal_difficulty() -> void:
	"""Normal mode - intelligent sector-based tracking"""
	print("Radio Operator: Using advanced sector analysis")
	
	if user_positions.size() == 0:
		print("Radio Operator: No enemy movements to analyze")
		return
	
	# Analyze movement patterns first
	_analyze_movement_patterns()
	
	# Try to find enemy position using sector elimination
	var found_position = _find_enemy_position_sectors()
	
	if found_position:
		# Process the movement trail to get final position
		_simulate_enemy_path()
	else:
		print("Radio Operator: No valid position found in remaining sectors")

func _process_hard_difficulty() -> void:
	"""Hard mode - advanced tracking with predictive analysis"""
	# For now, use normal mode logic
	_process_normal_difficulty()

func _analyze_movement_patterns() -> void:
	"""Analyze enemy movement patterns to eliminate impossible sectors"""
	# Reset compass counts
	user_compass_counts = [0, 0, 0, 0]
	
	# Count each type of movement
	for direction in user_positions:
		match direction:
			"NORTH": user_compass_counts[0] += 1
			"SOUTH": user_compass_counts[1] += 1
			"EAST": user_compass_counts[2] += 1
			"WEST": user_compass_counts[3] += 1
	
	print("Radio Operator: Movement analysis - N:", user_compass_counts[0], 
		  " S:", user_compass_counts[1], " E:", user_compass_counts[2], 
		  " W:", user_compass_counts[3])
	
	# Eliminate sectors based on movement patterns
	if user_compass_counts[0] >= 4:  # Too many north moves
		user_sector_starters[0] = true  # Eliminate sectors 1,2,3
		user_sector_starters[1] = true
		user_sector_starters[2] = true
		print("Radio Operator: Eliminated northern sectors (too many north moves)")
	
	if user_compass_counts[1] >= 4:  # Too many south moves  
		user_sector_starters[6] = true  # Eliminate sectors 7,8,9
		user_sector_starters[7] = true
		user_sector_starters[8] = true
		print("Radio Operator: Eliminated southern sectors (too many south moves)")

func _find_enemy_position_sectors() -> bool:
	"""Use sector-based elimination to find likely enemy position"""
	var max_attempts = 10  # Prevent infinite loops
	var attempts = 0
	
	while sector_trying == 10 and attempts < max_attempts:
		var sector_num = randi() % 9
		if not user_sector_starters[sector_num]:
			sector_trying = sector_num
			y_s_of_sector_check = 0
			x_s_of_sector_check = 0
			print("Radio Operator: Checking sector ", sector_num + 1)
			break
		attempts += 1
	
	if sector_trying == 10:
		print("Radio Operator: All sectors eliminated or exhausted")
		return false
	
	# Search within the chosen sector (bounded search)
	var sector_x_min = _grab_sector_x(sector_trying + 1)
	var sector_y_min = _grab_sector_y(sector_trying + 1)
	var positions_checked = 0
	var max_positions = 15  # Limit search to prevent infinite loops
	
	for y_offset in range(5):  # 5x5 sector
		for x_offset in range(5):
			if positions_checked >= max_positions:
				break
				
			var check_x = x_offset + sector_x_min
			var check_y = y_offset + sector_y_min
			
			if not positions_tried[check_y][check_x]:
				positions_tried[check_y][check_x] = true
				positions_checked += 1
				
				if _is_position_valid_for_enemy(Vector2i(check_x, check_y)):
					tried_position[0] = Vector2i(check_x, check_y)
					user_dead_zones[check_y][check_x] = true
					print("Radio Operator: Found valid position [", check_x, ", ", check_y, "]")
					return true
		
		if positions_checked >= max_positions:
			break
	
	# Mark this sector as exhausted
	user_sector_starters[sector_trying] = true
	sector_trying = 10
	return false

func _simulate_enemy_path() -> void:
	"""Simulate enemy path from starting position through all movements"""
	if tried_position.size() == 0 or user_positions.size() == 0:
		return
		
	var current_pos = tried_position[0]
	var path_valid = true
	var moves_simulated = 0
	
	# Follow each movement in sequence
	for i in range(min(user_positions.size(), 10)):  # Limit to prevent runaway
		var direction = user_positions[i]
		var delta = Vector2i.ZERO
		
		match direction:
			"NORTH": delta = Vector2i(0, -1)
			"SOUTH": delta = Vector2i(0, 1)
			"EAST": delta = Vector2i(1, 0)
			"WEST": delta = Vector2i(-1, 0)
		
		var next_pos = current_pos + delta
		
		if _is_position_valid_for_enemy(next_pos):
			current_pos = next_pos
			user_dead_zones[current_pos.y][current_pos.x] = true
			moves_simulated += 1
		else:
			path_valid = false
			break
	
	if path_valid and moves_simulated > 0:
		# Higher certainty based on successful path simulation
		var certainty = min(0.6 + (moves_simulated * 0.1), 1.0)
		radio_certainty = int(certainty * 10)
		
		emit_signal("enemy_position_updated", current_pos, certainty)
		print("Radio Operator: Enemy tracked to [", current_pos.x, ", ", current_pos.y, 
			  "] after ", moves_simulated, " moves (certainty: ", certainty, ")")
	else:
		print("Radio Operator: Path simulation failed - enemy position uncertain")

func _is_position_valid_for_enemy(pos: Vector2i) -> bool:
	"""Check if a position is valid for enemy submarine"""
	# Bounds check
	if pos.x < 0 or pos.x >= 15 or pos.y < 0 or pos.y >= 15:
		return false
	
	# Check if we've already determined enemy can't be here
	if user_dead_zones[pos.y][pos.x]:
		return false
	
	# Use AI manager's position validation (includes island checking)
	if ai_manager:
		return ai_manager.is_position_valid(pos)
	
	return true

func _grab_sector_x(sector: int) -> int:
	"""Get minimum X coordinate for sector (1-9)"""
	match sector:
		1, 4, 7: return 0
		2, 5, 8: return 5
		3, 6, 9: return 10
		_: return 0

func _grab_sector_y(sector: int) -> int:
	"""Get minimum Y coordinate for sector (1-9)"""
	match sector:
		1, 2, 3: return 0
		4, 5, 6: return 5
		7, 8, 9: return 10
		_: return 0

# Public interface functions
func track_enemy_movement(direction: String) -> void:
	"""Called when enemy moves - adds to movement log"""
	user_positions.append(direction)
	user_position_count += 1
	is_station_done = false  # Mark as needing processing
	print("Radio Operator: Logged enemy movement: ", direction, " (Total: ", user_position_count, ")")

func get_certainty() -> float:
	"""Returns current tracking certainty (0.0 to 1.0)"""
	return min(float(radio_certainty) / 10.0, 1.0)

func get_estimated_position() -> Vector2i:
	"""Returns best guess of enemy position"""
	if tried_position.size() > 0:
		return tried_position[0]
	return Vector2i.ZERO

func handle_enemy_surface() -> void:
	"""Called when enemy surfaces - clears all tracking"""
	user_positions.clear()
	user_position_count = 0
	user_read_tries = 0
	radio_certainty = 0
	sector_trying = 10
	
	for i in range(user_compass_counts.size()):
		user_compass_counts[i] = 0
	
	_initialize_tracking_arrays()
	print("Radio Operator: Enemy surfaced - all tracking reset")
	emit_signal("enemy_surfaced")

func set_game_over(is_over: bool) -> void:
	game_over = is_over
