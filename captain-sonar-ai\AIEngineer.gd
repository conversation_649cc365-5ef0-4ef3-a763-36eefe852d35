extends Node
class_name AIEngineer

# AI Engineer Station - Manages submarine systems and damage control
# Based on the C++ Engineer AI implementation

signal surface_recommended(should_surface: bool)
signal system_repaired(system_name: String)
signal movement_recommendation(north: bool, south: bool, west: bool, east: bool)

var ai_manager
var difficulty: AIProfile.Difficulty = AIProfile.Difficulty.NORMAL
var response_time: int = 20
var is_station_done: bool = true
var game_over: bool = false

# Engineer tracking variables (matching C++ implementation)
var should_surface: bool = false
var eng_next_path: int = 1
var engineer_uses: int = 0
var move_for_want: Array[bool] = [false, false, false, false]  # north, south, west, east

# System damage arrays (matching C++ engineer variables)
var north: Array[bool] = [false, false, false, false, false, false]
var south: Array[bool] = [false, false, false, false, false, false]
var east: Array[bool] = [false, false, false, false, false, false]
var west: Array[bool] = [false, false, false, false, false, false]

# System status tracking
var guns_broke: bool = false
var detection_broke: bool = false
var special_broke: bool = false

# Captain wants tracking (for normal+ difficulty)
var wants: Array[bool] = [false, false, false]  # guns, detection, silence

func setup(manager: AIManager, diff: AIProfile.Difficulty, response_ms: int) -> void:
	ai_manager = manager
	difficulty = diff
	response_time = response_ms

func is_done() -> bool:
	return is_station_done

func set_game_over(is_over: bool) -> void:
	game_over = is_over

func process_station() -> void:
	if game_over or is_station_done:
		return

	is_station_done = false

	# Process engineering work immediately
	match difficulty:
		AIProfile.Difficulty.EASY:
			_process_easy_difficulty()
		AIProfile.Difficulty.NORMAL:
			_process_normal_difficulty()
		AIProfile.Difficulty.HARD:
			_process_hard_difficulty()

	# Add response delay after processing
	await get_tree().create_timer(response_time / 1000.0).timeout

	is_station_done = true

func _process_easy_difficulty() -> void:
	# Easy engineer - marks damage based on movement direction, left to right priority
	if engineer_uses > 0:
		var compass_direction = _get_current_movement_direction()
		_mark_damage_easy(compass_direction)
		engineer_uses -= 1
		
		if engineer_uses == 0:
			should_surface = _easy_engineer_surface_check()
			emit_signal("surface_recommended", should_surface)
		
		_engineer_check()

func _process_normal_difficulty() -> void:
	# Normal engineer - smarter damage marking that considers captain's wants
	if engineer_uses > 0:
		var compass_direction = _get_current_movement_direction()
		_mark_damage_normal(compass_direction)
		engineer_uses -= 1
		
		if engineer_uses == 0:
			should_surface = _easy_engineer_surface_check()
			emit_signal("surface_recommended", should_surface)
			_update_movement_recommendations()
		
		_engineer_check()

func _process_hard_difficulty() -> void:
	# Hard engineer - placeholder for advanced logic
	_process_normal_difficulty()

func _get_current_movement_direction() -> String:
	# Get the current movement direction from AI manager's compass
	if ai_manager and ai_manager.ai_compass.size() > eng_next_path:
		return ai_manager.ai_compass[eng_next_path]
	return ""

func _mark_damage_easy(direction: String) -> void:
	# Easy difficulty - mark damage left to right, randomly if no preference
	var section_to_damage = []
	
	match direction:
		"NORTH":
			section_to_damage = north
		"SOUTH":
			section_to_damage = south
		"EAST":
			section_to_damage = east
		"WEST":
			section_to_damage = west
		_:
			return
	
	# Mark damage left to right (matching C++ easy engineer logic)
	var damage_index = -1
	for i in range(6):
		if not section_to_damage[i]:
			damage_index = i
			break
	
	# If no left-to-right slot available, pick randomly
	if damage_index == -1:
		damage_index = randi() % 6
	
	section_to_damage[damage_index] = true
	eng_next_path += 1

func _mark_damage_normal(direction: String) -> void:
	# Normal difficulty - intelligent damage marking considering captain's wants
	var section_to_damage = []
	var system_types = []

	match direction:
		"NORTH":
			section_to_damage = north
			system_types = [2, 0, 2, 1, 0, 5]  # special, guns, special, detection, guns, reactor
		"SOUTH":
			section_to_damage = south
			system_types = [1, 2, 0, 0, 5, 2]  # detection, special, guns, guns, reactor, special
		"EAST":
			section_to_damage = east
			system_types = [1, 2, 0, 5, 1, 5]  # detection, special, guns, reactor, detection, reactor
		"WEST":
			section_to_damage = west
			system_types = [0, 2, 1, 1, 5, 5]  # guns, special, detection, detection, reactor, reactor
		_:
			return

	_mark_damage_with_wants(section_to_damage, system_types)
	eng_next_path += 1

func _mark_damage_with_wants(section: Array[bool], system_types: Array[int]) -> void:
	# Mark damage intelligently based on what the captain wants to keep operational
	# system_types: 0=guns, 1=detection, 2=special, 5=reactor
	
	var damage_index = -1
	
	# First pass - try to damage systems the captain doesn't want
	for i in range(6):
		if not section[i]:
			var system_type = system_types[i]
			var is_wanted = false
			
			match system_type:
				0:  # guns
					is_wanted = wants[0]
				1:  # detection
					is_wanted = wants[1]
				2:  # special/silence
					is_wanted = wants[2]
				5:  # reactor (always avoid if possible)
					is_wanted = true
			
			if not is_wanted:
				damage_index = i
				break
	
	# Second pass - if all wanted systems, damage left to right
	if damage_index == -1:
		for i in range(6):
			if not section[i]:
				damage_index = i
				break
	
	# Third pass - if all damaged, pick randomly
	if damage_index == -1:
		damage_index = randi() % 6
	
	section[damage_index] = true
	
	# Update movement recommendations if captain wants this system
	var system_type = system_types[damage_index]
	if wants[system_type] if system_type < 3 else false:
		_suggest_movement_to_preserve_system(system_type)

func _suggest_movement_to_preserve_system(system_type: int) -> void:
	# Suggest movement directions that would help preserve wanted systems
	match system_type:
		0:  # guns - suggest moving east to avoid more gun damage
			move_for_want[3] = true  # east
		1:  # detection - suggest moving north to avoid more detection damage
			move_for_want[0] = true  # north
		2:  # special - suggest moving south to avoid more special damage
			move_for_want[1] = true  # south

func _engineer_check() -> void:
	# Check system status and handle repairs (matching C++ EngineerCheck function)
	
	# Check guns system
	if west[0] or north[1] or north[4] or south[2] or south[3] or east[2]:
		guns_broke = true
	
	# Check detection system
	if west[2] or west[3] or north[3] or south[0] or east[0] or east[4]:
		detection_broke = true
	
	# Check special system
	if west[1] or north[0] or north[2] or south[1] or south[5] or east[1]:
		special_broke = true
	
	# Handle line repairs (yellow, orange, grey lines)
	_check_line_repairs()
	
	# Handle full section damage
	_check_section_damage()
	
	# Handle reactor damage
	_check_reactor_damage()
	
	# Update game state
	_update_submarine_systems()

func _check_line_repairs() -> void:
	# Yellow line repair (matching C++ exact positions)
	if west[0] and west[1] and west[2] and east[2]:
		west[0] = false
		west[1] = false
		west[2] = false
		east[2] = false
		emit_signal("system_repaired", "yellow_line")
		print("Engineer: I'm doing Yellow line repaired!")

	# Orange line repair (matching C++ exact positions)
	if north[0] and north[1] and north[2] and east[0]:
		north[0] = false
		north[1] = false
		north[2] = false
		east[0] = false
		emit_signal("system_repaired", "orange_line")
		print("Engineer: I'm doing Orange line repaired!")

	# Grey line repair (matching C++ exact positions)
	if south[0] and south[1] and south[2] and east[1]:
		south[0] = false
		south[1] = false
		south[2] = false
		east[1] = false
		emit_signal("system_repaired", "grey_line")
		print("Engineer: I'm doing Grey line repaired!")

	# Additional line repairs from C++ code
	# Check for other repair combinations
	_check_additional_line_repairs()

func _check_additional_line_repairs() -> void:
	# Additional repair patterns found in C++ code
	# These are more complex repair combinations

	# Check for cross-section repairs
	if north[1] and south[2] and west[0] and east[2]:  # Guns cross-repair
		north[1] = false
		south[2] = false
		west[0] = false
		east[2] = false
		emit_signal("system_repaired", "guns_cross_repair")
		print("Engineer: I'm doing Guns cross-repair completed!")

	# Check for detection system repairs
	if north[3] and south[0] and west[2] and east[0]:  # Detection cross-repair
		north[3] = false
		south[0] = false
		west[2] = false
		east[0] = false
		emit_signal("system_repaired", "detection_cross_repair")
		print("Engineer: I'm doing Detection cross-repair completed!")

	# Check for special system repairs
	if north[0] and south[1] and west[1] and east[1]:  # Special cross-repair
		north[0] = false
		south[1] = false
		west[1] = false
		east[1] = false
		emit_signal("system_repaired", "special_cross_repair")
		print("Engineer: I'm doing Special cross-repair completed!")

func _check_section_damage() -> void:
	# Check for full section damage and repair if all damaged
	if _all_section_damaged(west):
		_clear_section(west)
		_damage_submarine_health()
		emit_signal("system_repaired", "west_section")
	
	if _all_section_damaged(north):
		_clear_section(north)
		_damage_submarine_health()
		emit_signal("system_repaired", "north_section")
	
	if _all_section_damaged(south):
		_clear_section(south)
		_damage_submarine_health()
		emit_signal("system_repaired", "south_section")
	
	if _all_section_damaged(east):
		_clear_section(east)
		_damage_submarine_health()
		emit_signal("system_repaired", "east_section")

func _check_reactor_damage() -> void:
	# Check reactor damage (specific positions across sections matching C++ exactly)
	if west[4] and west[5] and north[5] and south[4] and east[3] and east[5]:
		west[4] = false
		west[5] = false
		north[5] = false
		south[4] = false
		east[3] = false
		east[5] = false
		_damage_submarine_health()
		emit_signal("system_repaired", "reactor")
		print("Engineer: I'm doing Critical reactor damage repaired! Health lost.")

	# Check for partial reactor damage patterns
	var reactor_damage_count = 0
	if west[4]: reactor_damage_count += 1
	if west[5]: reactor_damage_count += 1
	if north[5]: reactor_damage_count += 1
	if south[4]: reactor_damage_count += 1
	if east[3]: reactor_damage_count += 1
	if east[5]: reactor_damage_count += 1

	# Warn if reactor is getting close to critical
	if reactor_damage_count >= 4:
		should_surface = true
		print("Engineer: I'm doing Reactor damage critical - recommending surface!")
	elif reactor_damage_count >= 3:
		print("Engineer: I'm doing Reactor damage severe - caution advised!")

func _all_section_damaged(section: Array[bool]) -> bool:
	for damaged in section:
		if not damaged:
			return false
	return true

func _clear_section(section: Array[bool]) -> void:
	for i in range(section.size()):
		section[i] = false

func _damage_submarine_health() -> void:
	if ai_manager:
		ai_manager.ai_health -= 1
		if ai_manager.ai_health <= 0:
			ai_manager.set_game_over(true)

func _easy_engineer_surface_check() -> bool:
	# Check if critical systems are damaged and recommend surfacing
	return west[3] or north[3] or north[4] or south[3] or south[5] or east[4]

func _update_movement_recommendations() -> void:
	# Send movement recommendations to captain
	emit_signal("movement_recommendation", move_for_want[0], move_for_want[1], move_for_want[2], move_for_want[3])
	# Reset recommendations
	move_for_want = [false, false, false, false]

func _update_submarine_systems() -> void:
	# Update the submarine's system status in GameState
	if ai_manager and ai_manager.team_id and GameState and GameState.teams.has(ai_manager.team_id):
		var sub_state: SubmarineState = GameState.teams[ai_manager.team_id]
		
		# Update system states based on damage
		sub_state.systems["weapons"] = SubmarineState.SystemState.DAMAGED if guns_broke else SubmarineState.SystemState.OPERATIONAL
		sub_state.systems["sonar"] = SubmarineState.SystemState.DAMAGED if detection_broke else SubmarineState.SystemState.OPERATIONAL
		sub_state.systems["special"] = SubmarineState.SystemState.DAMAGED if special_broke else SubmarineState.SystemState.OPERATIONAL
		sub_state.health = ai_manager.ai_health
		
		GameState.emit_signal("state_updated", ai_manager.team_id)

func start_engineering_work(uses: int) -> void:
	engineer_uses = uses
	is_station_done = false

func update_captain_wants(guns: bool, detection: bool, silence: bool) -> void:
	wants[0] = guns
	wants[1] = detection
	wants[2] = silence

func get_damage_status() -> Dictionary:
	return {
		"north": north.duplicate(),
		"south": south.duplicate(),
		"east": east.duplicate(),
		"west": west.duplicate(),
		"guns_broke": guns_broke,
		"detection_broke": detection_broke,
		"special_broke": special_broke
	}
