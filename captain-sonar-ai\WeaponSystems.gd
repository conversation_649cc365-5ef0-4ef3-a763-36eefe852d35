extends Node
class_name WeaponSystems

# Complete weapon and combat systems for Captain <PERSON><PERSON>
# Based on the C++ weapon usage and combat logic

signal weapon_fired(weapon_type: String, position: Vector2i, target: Vector2i)
signal weapon_hit(weapon_type: String, position: Vector2i, damage: int)
signal weapon_miss(weapon_type: String, position: Vector2i)

var ai_manager

# Weapon tracking
var active_torpedoes: Array = []
var active_mines: Array = []
var active_drones: Array = []
var sonar_pings: Array = []

# Combat results
var last_torpedo_result: Dictionary = {}
var last_mine_result: Dictionary = {}
var last_drone_result: Dictionary = {}

func setup(manager: AIManager) -> void:
	ai_manager = manager

# Torpedo system (matching C++ torpedo logic)
func launch_torpedo(start_pos: Vector2i, target_pos: Vector2i) -> bool:
	if not ai_manager:
		return false
	
	# Check if torpedo can reach target
	if not ai_manager.torpedo_check(start_pos.y, start_pos.x, target_pos.y, target_pos.x):
		print("Torpedo cannot reach target - path blocked")
		return false
	
	var torpedo_data = {
		"start": start_pos,
		"target": target_pos,
		"path": _calculate_torpedo_path(start_pos, target_pos),
		"active": true
	}
	
	active_torpedoes.append(torpedo_data)
	emit_signal("weapon_fired", "torpedo", start_pos, target_pos)
	
	# Check for hit
	var hit_result = _check_torpedo_hit(target_pos)
	last_torpedo_result = hit_result
	
	if hit_result.hit:
		emit_signal("weapon_hit", "torpedo", target_pos, hit_result.damage)
		print("Torpedo hit at [", target_pos.x, ", ", target_pos.y, "] - Damage: ", hit_result.damage)
	else:
		emit_signal("weapon_miss", "torpedo", target_pos)
		print("Torpedo missed at [", target_pos.x, ", ", target_pos.y, "]")
	
	return true

func _calculate_torpedo_path(start: Vector2i, target: Vector2i) -> Array:
	# Calculate torpedo path using pathfinding
	var path = []
	var current = start
	
	while current != target:
		path.append(current)
		
		# Move towards target
		var delta = target - current
		if abs(delta.x) > abs(delta.y):
			current.x += sign(delta.x)
		else:
			current.y += sign(delta.y)
		
		# Safety check
		if path.size() > 10:  # Max torpedo range
			break
	
	path.append(target)
	return path

func _check_torpedo_hit(target_pos: Vector2i) -> Dictionary:
	# Check if torpedo hits enemy submarine
	var result = {"hit": false, "damage": 0, "type": "miss"}
	
	if not ai_manager or not GameState:
		return result
	
	# Get enemy position
	var enemy_team_id = "alpha" if ai_manager.team_id == "bravo" else "bravo"
	if not GameState.teams.has(enemy_team_id):
		return result
	
	var enemy_pos = GameState.teams[enemy_team_id].position
	var distance = target_pos.distance_to(enemy_pos)
	
	if distance == 0:
		# Direct hit
		result.hit = true
		result.damage = 2
		result.type = "direct"
	elif distance == 1:
		# Indirect hit (adjacent)
		result.hit = true
		result.damage = 1
		result.type = "indirect"
	
	return result

# Mine system (matching C++ mine logic)
func place_mine(position: Vector2i) -> bool:
	if not ai_manager:
		return false
	
	# Check if position is valid for mine placement
	if not ai_manager.no_no_areas(position.y, position.x, false, true):
		print("Cannot place mine - invalid position")
		return false
	
	var mine_data = {
		"position": position,
		"active": true,
		"armed": true
	}
	
	active_mines.append(mine_data)
	emit_signal("weapon_fired", "mine", position, position)
	print("Mine placed at [", position.x, ", ", position.y, "]")
	
	return true

func activate_mine(mine_index: int) -> bool:
	if mine_index < 0 or mine_index >= active_mines.size():
		return false
	
	var mine = active_mines[mine_index]
	if not mine.active:
		return false
	
	var mine_pos = mine.position
	var hit_result = _check_mine_explosion(mine_pos)
	last_mine_result = hit_result
	
	if hit_result.hit:
		emit_signal("weapon_hit", "mine", mine_pos, hit_result.damage)
		print("Mine exploded at [", mine_pos.x, ", ", mine_pos.y, "] - Damage: ", hit_result.damage)
	else:
		emit_signal("weapon_miss", "mine", mine_pos)
		print("Mine exploded but missed target")
	
	# Remove mine after explosion
	active_mines.remove_at(mine_index)
	return true

func _check_mine_explosion(mine_pos: Vector2i) -> Dictionary:
	# Check if mine explosion hits enemy
	var result = {"hit": false, "damage": 0, "type": "miss"}
	
	if not ai_manager or not GameState:
		return result
	
	# Get enemy position
	var enemy_team_id = "alpha" if ai_manager.team_id == "bravo" else "bravo"
	if not GameState.teams.has(enemy_team_id):
		return result
	
	var enemy_pos = GameState.teams[enemy_team_id].position
	var distance = mine_pos.distance_to(enemy_pos)
	
	if distance <= 1:  # Mine explosion radius
		result.hit = true
		result.damage = 2 if distance == 0 else 1
		result.type = "direct" if distance == 0 else "indirect"
	
	return result

# Drone system (matching C++ drone logic)
func launch_drone(sector: int = 0) -> Dictionary:
	if not ai_manager:
		return {}
	
	var drone_data = {
		"sector": sector,
		"active": true,
		"scan_result": {}
	}
	
	# Perform sector scan
	var scan_result = _perform_drone_scan(sector)
	drone_data.scan_result = scan_result
	last_drone_result = scan_result
	
	active_drones.append(drone_data)
	emit_signal("weapon_fired", "drone", Vector2i.ZERO, Vector2i.ZERO)
	
	print("Drone launched - Sector ", sector, " scan: ", scan_result)
	return scan_result

func _perform_drone_scan(sector: int) -> Dictionary:
	# Perform drone sector scan
	var result = {"enemy_detected": false, "sector": sector, "confidence": 0.0}
	
	if not ai_manager or not GameState:
		return result
	
	# Get enemy position
	var enemy_team_id = "alpha" if ai_manager.team_id == "bravo" else "bravo"
	if not GameState.teams.has(enemy_team_id):
		return result
	
	var enemy_pos = GameState.teams[enemy_team_id].position
	var enemy_sector = ai_manager.sector_positions(enemy_pos.y, enemy_pos.x)
	
	if sector == 0:
		# General scan - detect if enemy is in any sector
		result.enemy_detected = true
		result.sector = enemy_sector
		result.confidence = 0.8
	elif sector == enemy_sector:
		# Targeted scan hit
		result.enemy_detected = true
		result.confidence = 1.0
	else:
		# Targeted scan miss
		result.enemy_detected = false
		result.confidence = 0.0
	
	return result

# Sonar system (matching C++ sonar logic)
func activate_sonar(scan_type: String = "sector") -> Dictionary:
	if not ai_manager:
		return {}
	
	var sonar_data = {
		"type": scan_type,
		"active": true,
		"result": {}
	}
	
	var scan_result = _perform_sonar_scan(scan_type)
	sonar_data.result = scan_result
	
	sonar_pings.append(sonar_data)
	emit_signal("weapon_fired", "sonar", Vector2i.ZERO, Vector2i.ZERO)
	
	print("Sonar activated - Type: ", scan_type, " Result: ", scan_result)
	return scan_result

func _perform_sonar_scan(scan_type: String) -> Dictionary:
	# Perform sonar scan
	var result = {"type": scan_type, "data": {}}
	
	if not ai_manager or not GameState:
		return result
	
	# Get enemy position
	var enemy_team_id = "alpha" if ai_manager.team_id == "bravo" else "bravo"
	if not GameState.teams.has(enemy_team_id):
		return result
	
	var enemy_pos = GameState.teams[enemy_team_id].position
	
	match scan_type:
		"sector":
			result.data["sector"] = ai_manager.sector_positions(enemy_pos.y, enemy_pos.x)
		"position":
			# Give approximate position with some noise
			var noise_x = randi() % 3 - 1
			var noise_y = randi() % 3 - 1
			result.data["x"] = enemy_pos.x + noise_x
			result.data["y"] = enemy_pos.y + noise_y
		"exact":
			result.data["x"] = enemy_pos.x
			result.data["y"] = enemy_pos.y
	
	return result

# Silence system (matching C++ silence logic)
func activate_silence(movement_count: int = 4) -> bool:
	if not ai_manager:
		return false
	
	print("Silence activated - ", movement_count, " hidden movements")
	
	# Notify radio operator about silence usage
	if ai_manager.ai_radio_operator:
		ai_manager.ai_radio_operator.handle_silence_movement()
	
	return true

# Utility functions
func get_weapon_status() -> Dictionary:
	return {
		"torpedoes": active_torpedoes.size(),
		"mines": active_mines.size(),
		"drones": active_drones.size(),
		"sonar_pings": sonar_pings.size(),
		"last_torpedo": last_torpedo_result,
		"last_mine": last_mine_result,
		"last_drone": last_drone_result
	}

func clear_expired_weapons() -> void:
	# Clean up expired weapons
	active_torpedoes = active_torpedoes.filter(func(t): return t.active)
	active_drones = active_drones.filter(func(d): return d.active)
	
	# Limit sonar ping history
	if sonar_pings.size() > 10:
		sonar_pings = sonar_pings.slice(-10)

func get_mine_positions() -> Array:
	var positions = []
	for mine in active_mines:
		positions.append(mine.position)
	return positions
