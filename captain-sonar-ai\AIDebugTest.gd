extends Node

# Simple AI debug test to isolate the crash issue

func _ready() -> void:
	print("=== AI Debug Test Starting ===")
	
	# Test 1: Basic class instantiation
	print("\n--- Test 1: Class Instantiation ---")
	test_class_instantiation()
	
	# Test 2: GameState initialization
	print("\n--- Test 2: GameState Initialization ---")
	test_gamestate_initialization()
	
	# Test 3: AI Manager setup
	print("\n--- Test 3: AI Manager Setup ---")
	test_ai_manager_setup()
	
	# Test 4: Simple movement simulation
	print("\n--- Test 4: Movement Simulation ---")
	test_movement_simulation()
	
	print("\n=== AI Debug Test Completed ===")

func test_class_instantiation() -> void:
	print("Testing class instantiation...")
	
	try:
		var profile = AIProfile.new()
		print("✅ AIProfile: OK")
		
		var manager = AIManager.new()
		print("✅ AIManager: OK")
		
		var captain = AICaptain.new()
		print("✅ AICaptain: OK")
		
		var engineer = AIEngineer.new()
		print("✅ AIEngineer: OK")
		
		var first_mate = AIFirstMate.new()
		print("✅ AIFirstMate: OK")
		
		var radio_op = AIRadioOperator.new()
		print("✅ AIRadioOperator: OK")
		
		var weapons = WeaponSystems.new()
		print("✅ WeaponSystems: OK")
		
		var timing = AITiming.new()
		print("✅ AITiming: OK")
		
		# Clean up
		manager.queue_free()
		captain.queue_free()
		engineer.queue_free()
		first_mate.queue_free()
		radio_op.queue_free()
		weapons.queue_free()
		timing.queue_free()
		
	except error:
		print("❌ Class instantiation failed: ", error)

func test_gamestate_initialization() -> void:
	print("Testing GameState initialization...")
	
	try:
		if not GameState:
			print("❌ GameState not available")
			return
		
		# Create simple test map
		var test_map = []
		for x in range(15):
			test_map.append([])
			for y in range(15):
				test_map[x].append(false)  # All water
		
		print("Initializing GameState with test map...")
		GameState.initialize_game(test_map)
		print("✅ GameState initialized successfully")
		
		# Check if teams exist
		if GameState.teams.has("alpha") and GameState.teams.has("bravo"):
			print("✅ Teams created successfully")
		else:
			print("❌ Teams not created properly")
		
	except error:
		print("❌ GameState initialization failed: ", error)

func test_ai_manager_setup() -> void:
	print("Testing AI Manager setup...")
	
	try:
		if not GameState or not GameState.ai_manager:
			print("❌ AI Manager not available in GameState")
			return
		
		var ai_manager = GameState.ai_manager
		print("AI Manager found: ", ai_manager != null)
		
		if ai_manager.ai_captain:
			print("✅ AI Captain available")
		else:
			print("❌ AI Captain not available")
		
		if ai_manager.ai_engineer:
			print("✅ AI Engineer available")
		else:
			print("❌ AI Engineer not available")
		
		if ai_manager.ai_first_mate:
			print("✅ AI First Mate available")
		else:
			print("❌ AI First Mate not available")
		
		if ai_manager.ai_radio_operator:
			print("✅ AI Radio Operator available")
		else:
			print("❌ AI Radio Operator not available")
		
	except error:
		print("❌ AI Manager setup test failed: ", error)

func test_movement_simulation() -> void:
	print("Testing movement simulation...")
	
	try:
		if not GameState:
			print("❌ GameState not available")
			return
		
		print("Simulating player movement...")
		
		# Get initial positions
		var alpha_pos = GameState.teams["alpha"].position if GameState.teams.has("alpha") else Vector2i.ZERO
		var bravo_pos = GameState.teams["bravo"].position if GameState.teams.has("bravo") else Vector2i.ZERO
		
		print("Initial positions - Alpha: ", alpha_pos, ", Bravo: ", bravo_pos)
		
		# Simulate a player move
		print("Processing captain move: NORTH")
		GameState.process_captain_move("alpha", "NORTH")
		
		# Wait a moment
		await get_tree().create_timer(1.0).timeout
		
		print("Triggering AI turn...")
		GameState.trigger_ai_turn()
		
		# Wait for AI processing
		await get_tree().create_timer(2.0).timeout
		
		# Check final positions
		var final_alpha_pos = GameState.teams["alpha"].position if GameState.teams.has("alpha") else Vector2i.ZERO
		var final_bravo_pos = GameState.teams["bravo"].position if GameState.teams.has("bravo") else Vector2i.ZERO
		
		print("Final positions - Alpha: ", final_alpha_pos, ", Bravo: ", final_bravo_pos)
		print("✅ Movement simulation completed")
		
	except error:
		print("❌ Movement simulation failed: ", error)

func _on_timeout() -> void:
	print("Test timeout reached")
	get_tree().quit()
