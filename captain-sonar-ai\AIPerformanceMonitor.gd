extends Node
class_name AIPerformanceMonitor

# AI Performance Monitoring System
# Tracks timing, efficiency metrics, and system performance for optimization

signal performance_report_ready(report: Dictionary)

@export var enable_monitoring: bool = true
@export var report_interval: float = 5.0  # Report every 5 seconds

# Performance metrics
var turn_start_time: float = 0.0
var turn_end_time: float = 0.0
var station_timings: Dictionary = {}
var total_turns: int = 0
var average_turn_time: float = 0.0
var min_turn_time: float = INF
var max_turn_time: float = 0.0

# Station-specific metrics
var station_performance: Dictionary = {
	"captain": {"total_time": 0.0, "calls": 0, "avg_time": 0.0},
	"engineer": {"total_time": 0.0, "calls": 0, "avg_time": 0.0},
	"first_mate": {"total_time": 0.0, "calls": 0, "avg_time": 0.0},
	"radio_operator": {"total_time": 0.0, "calls": 0, "avg_time": 0.0}
}

# System metrics
var memory_usage_samples: Array[float] = []
var frame_time_samples: Array[float] = []
var signal_emissions: int = 0
var redundant_operations: int = 0

# Reporting timer
var report_timer: Timer

func _ready() -> void:
	if not enable_monitoring:
		return
	
	# Setup reporting timer
	report_timer = Timer.new()
	report_timer.wait_time = report_interval
	report_timer.timeout.connect(_generate_performance_report)
	report_timer.autostart = true
	add_child(report_timer)
	
	# Start periodic performance sampling
	var sample_timer = Timer.new()
	sample_timer.wait_time = 0.5  # Sample every 0.5 seconds
	sample_timer.timeout.connect(_sample_performance_metrics)
	sample_timer.autostart = true
	add_child(sample_timer)

func start_turn_monitoring() -> void:
	"""Start monitoring a new AI turn"""
	if not enable_monitoring:
		return

	turn_start_time = Time.get_time_dict_from_system()["unix"]
	station_timings.clear()

func end_turn_monitoring() -> void:
	"""End monitoring for the current AI turn"""
	if not enable_monitoring:
		return

	turn_end_time = Time.get_time_dict_from_system()["unix"]
	var turn_duration = turn_end_time - turn_start_time
	
	# Update turn statistics
	total_turns += 1
	var total_time = average_turn_time * (total_turns - 1) + turn_duration
	average_turn_time = total_time / total_turns
	
	min_turn_time = min(min_turn_time, turn_duration)
	max_turn_time = max(max_turn_time, turn_duration)

func start_station_monitoring(station_name: String) -> void:
	"""Start monitoring a specific AI station"""
	if not enable_monitoring:
		return

	station_timings[station_name] = Time.get_time_dict_from_system()["unix"]

func end_station_monitoring(station_name: String) -> void:
	"""End monitoring for a specific AI station"""
	if not enable_monitoring or not station_timings.has(station_name):
		return

	var start_time = station_timings[station_name]
	var end_time = Time.get_time_dict_from_system()["unix"]
	var duration = end_time - start_time
	
	# Update station performance metrics
	if station_performance.has(station_name):
		var perf = station_performance[station_name]
		perf["total_time"] += duration
		perf["calls"] += 1
		perf["avg_time"] = perf["total_time"] / perf["calls"]

func track_signal_emission() -> void:
	"""Track signal emissions for efficiency analysis"""
	if enable_monitoring:
		signal_emissions += 1

func track_redundant_operation() -> void:
	"""Track redundant operations that could be optimized"""
	if enable_monitoring:
		redundant_operations += 1

func _sample_performance_metrics() -> void:
	"""Sample performance metrics periodically"""
	if not enable_monitoring:
		return

	# Sample memory usage (using correct Godot 4 API)
	var memory_usage = OS.get_static_memory_peak_usage()
	memory_usage_samples.append(memory_usage)

	# Keep only last 100 samples
	if memory_usage_samples.size() > 100:
		memory_usage_samples.pop_front()

	# Sample frame time (using delta time as approximation)
	var frame_time = get_process_delta_time()
	frame_time_samples.append(frame_time)

	# Keep only last 100 samples
	if frame_time_samples.size() > 100:
		frame_time_samples.pop_front()

func _generate_performance_report() -> void:
	"""Generate and emit a comprehensive performance report"""
	if not enable_monitoring:
		return
	
	var report = {
		"timestamp": Time.get_time_dict_from_system(),
		"turn_metrics": {
			"total_turns": total_turns,
			"average_turn_time": average_turn_time,
			"min_turn_time": min_turn_time,
			"max_turn_time": max_turn_time
		},
		"station_performance": station_performance.duplicate(true),
		"system_metrics": {
			"signal_emissions": signal_emissions,
			"redundant_operations": redundant_operations,
			"avg_memory_usage": _calculate_average(memory_usage_samples),
			"avg_frame_time": _calculate_average(frame_time_samples)
		},
		"efficiency_score": _calculate_efficiency_score()
	}
	
	emit_signal("performance_report_ready", report)
	
	# Print summary if debug mode is enabled
	if OS.is_debug_build():
		_print_performance_summary(report)

func _calculate_average(samples: Array[float]) -> float:
	"""Calculate average of sample array"""
	if samples.is_empty():
		return 0.0
	
	var sum = 0.0
	for sample in samples:
		sum += sample
	
	return sum / samples.size()

func _calculate_efficiency_score() -> float:
	"""Calculate overall AI efficiency score (0-100)"""
	var score = 100.0
	
	# Penalize for long turn times
	if average_turn_time > 1.0:
		score -= (average_turn_time - 1.0) * 20
	
	# Penalize for redundant operations
	if total_turns > 0:
		var redundancy_ratio = float(redundant_operations) / total_turns
		score -= redundancy_ratio * 30
	
	# Penalize for excessive signal emissions
	if total_turns > 0:
		var signal_ratio = float(signal_emissions) / total_turns
		if signal_ratio > 10:  # More than 10 signals per turn is excessive
			score -= (signal_ratio - 10) * 5
	
	return max(0.0, min(100.0, score))

func _print_performance_summary(report: Dictionary) -> void:
	"""Print a concise performance summary"""
	print("=== AI Performance Summary ===")
	print("Turns: ", report["turn_metrics"]["total_turns"])
	print("Avg Turn Time: ", "%.3f" % report["turn_metrics"]["average_turn_time"], "s")
	print("Efficiency Score: ", "%.1f" % report["efficiency_score"], "/100")
	print("Signal Emissions: ", report["system_metrics"]["signal_emissions"])
	print("Redundant Ops: ", report["system_metrics"]["redundant_operations"])
	print("=============================")

func reset_metrics() -> void:
	"""Reset all performance metrics"""
	turn_start_time = 0.0
	turn_end_time = 0.0
	station_timings.clear()
	total_turns = 0
	average_turn_time = 0.0
	min_turn_time = INF
	max_turn_time = 0.0
	
	for station in station_performance:
		station_performance[station] = {"total_time": 0.0, "calls": 0, "avg_time": 0.0}
	
	memory_usage_samples.clear()
	frame_time_samples.clear()
	signal_emissions = 0
	redundant_operations = 0

func get_current_metrics() -> Dictionary:
	"""Get current performance metrics"""
	return {
		"turn_metrics": {
			"total_turns": total_turns,
			"average_turn_time": average_turn_time,
			"min_turn_time": min_turn_time if min_turn_time != INF else 0.0,
			"max_turn_time": max_turn_time
		},
		"station_performance": station_performance.duplicate(true),
		"efficiency_score": _calculate_efficiency_score()
	}
