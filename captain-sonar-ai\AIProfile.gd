extends Resource
class_name AIProfile

# AI Difficulty levels matching the C++ implementation
enum Difficulty { EASY = 1, NORMAL = 2, HARD = 3 }

# AI Station Types
enum StationType { CAPTAIN, ENGINEER, FIRST_MATE, RADIO_OPERATOR }

# AI Personalities for different behavior patterns
enum Personality { STALKER, AGGRESSIVE, DEFENSIVE, TRICKSTER }

# Individual station configurations
@export var captain_difficulty: Difficulty = Difficulty.NORMAL
@export var engineer_difficulty: Difficulty = Difficulty.NORMAL
@export var first_mate_difficulty: Difficulty = Difficulty.NORMAL
@export var radio_operator_difficulty: Difficulty = Difficulty.NORMAL

# Response times for each station (in milliseconds, matching C++ implementation)
@export var captain_response_time: int = 20
@export var engineer_response_time: int = 20
@export var first_mate_response_time: int = 20
@export var radio_operator_response_time: int = 20

# AI behavior settings
@export var personality: Personality = Personality.AGGRESSIVE
@export var preferred_weapon: String = "Torpedo"
@export var surface_aversion: float = 0.2
@export var be_in_range_of_user: int = 0  # For personality - sometimes AI wants to be closer
@export var chance_to_change_tactic: float = 0.1

# Equipment maximums (matching C++ equipment struct)
@export var max_mines: int = 3
@export var max_torpedo: int = 4
@export var max_drones: int = 3
@export var max_sonar: int = 3
@export var max_silence: int = 6
@export var max_scenario: int = 1
