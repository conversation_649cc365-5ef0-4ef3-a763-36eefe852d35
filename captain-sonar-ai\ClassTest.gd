extends Node

# Simple test to verify all AI classes are properly defined and can be instantiated

func _ready() -> void:
	print("Testing AI class instantiation...")
	
	# Test AIProfile
	var profile = AIProfile.new()
	if profile:
		print("✅ AIProfile: OK")
	else:
		print("❌ AIProfile: FAILED")
	
	# Test AIManager
	var manager = AIManager.new()
	if manager:
		print("✅ AIManager: OK")
	else:
		print("❌ AIManager: FAILED")
	
	# Test AICaptain
	var captain = AICaptain.new()
	if captain:
		print("✅ AICaptain: OK")
	else:
		print("❌ AICaptain: FAILED")
	
	# Test AIEngineer
	var engineer = AIEngineer.new()
	if engineer:
		print("✅ AIEngineer: OK")
	else:
		print("❌ AIEngineer: FAILED")
	
	# Test AIFirstMate
	var first_mate = AIFirstMate.new()
	if first_mate:
		print("✅ AIFirstMate: OK")
	else:
		print("❌ AIFirstMate: FAILED")
	
	# Test AIRadioOperator
	var radio_op = AIRadioOperator.new()
	if radio_op:
		print("✅ AIRadioOperator: OK")
	else:
		print("❌ AIRadioOperator: FAILED")
	
	# Test WeaponSystems
	var weapons = WeaponSystems.new()
	if weapons:
		print("✅ WeaponSystems: OK")
	else:
		print("❌ WeaponSystems: FAILED")
	
	# Test AITiming
	var timing = AITiming.new()
	if timing:
		print("✅ AITiming: OK")
	else:
		print("❌ AITiming: FAILED")
	
	print("Class instantiation test completed!")
	
	# Clean up
	if manager:
		manager.queue_free()
	if captain:
		captain.queue_free()
	if engineer:
		engineer.queue_free()
	if first_mate:
		first_mate.queue_free()
	if radio_op:
		radio_op.queue_free()
	if weapons:
		weapons.queue_free()
	if timing:
		timing.queue_free()
