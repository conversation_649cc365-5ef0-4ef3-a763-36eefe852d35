list=[{
"base": &"Node",
"class": &"AICaptain",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AICaptain.gd"
}, {
"base": &"Node",
"class": &"AIEngineer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIEngineer.gd"
}, {
"base": &"Node",
"class": &"AIFirstMate",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIFirstMate.gd"
}, {
"base": &"Node",
"class": &"AIManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIManager.gd"
}, {
"base": &"Resource",
"class": &"AIProfile",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIProfile.gd"
}, {
"base": &"Node",
"class": &"AIRadioOperator",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AIRadioOperator.gd"
}, {
"base": &"Node",
"class": &"AITiming",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AITiming.gd"
}, {
"base": &"Resource",
"class": &"SubmarineState",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://SubmarineState.gd"
}, {
"base": &"Node",
"class": &"WeaponSystems",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://WeaponSystems.gd"
}]
