extends Node

# AI Test Script - Verifies AI functionality matches C++ implementation
# Run this script to test various AI behaviors and validate integration

var test_results: Array[Dictionary] = []
var ai_manager: AIManager

func _ready() -> void:
	print("Starting AI Integration Tests...")
	_run_all_tests()

func _run_all_tests() -> void:
	# Initialize test environment
	_setup_test_environment()
	
	# Run individual tests
	_test_ai_profile_creation()
	_test_ai_manager_initialization()
	_test_captain_movement_decisions()
	_test_engineer_damage_tracking()
	_test_first_mate_equipment_charging()
	_test_radio_operator_tracking()
	_test_ai_integration_with_gamestate()
	
	# Print results
	_print_test_results()

func _setup_test_environment() -> void:
	# Create a test AI manager
	ai_manager = AIManager.new()
	add_child(ai_manager)
	
	# Initialize GameState if not already done
	if not GameState:
		print("Warning: GameState not available for testing")
		return
	
	# Create test map data
	var test_map = []
	for x in range(15):
		test_map.append([])
		for y in range(15):
			test_map[x].append(false)  # All water for testing
	
	GameState.initialize_game(test_map)

func _test_ai_profile_creation() -> void:
	var test_name = "AI Profile Creation"
	var success = true
	var details = ""
	
	try:
		var profile = AIProfile.new()
		
		# Test default values
		if profile.captain_difficulty != AIProfile.Difficulty.NORMAL:
			success = false
			details += "Captain difficulty default incorrect. "
		
		if profile.engineer_difficulty != AIProfile.Difficulty.NORMAL:
			success = false
			details += "Engineer difficulty default incorrect. "
		
		# Test setting values
		profile.captain_difficulty = AIProfile.Difficulty.EASY
		profile.max_torpedo = 5
		
		if profile.captain_difficulty != AIProfile.Difficulty.EASY:
			success = false
			details += "Captain difficulty setting failed. "
		
		if profile.max_torpedo != 5:
			success = false
			details += "Equipment max setting failed. "
		
		if success:
			details = "All profile tests passed"
		
	except:
		success = false
		details = "Exception during profile creation"
	
	_add_test_result(test_name, success, details)

func _test_ai_manager_initialization() -> void:
	var test_name = "AI Manager Initialization"
	var success = true
	var details = ""
	
	try:
		if not ai_manager:
			success = false
			details = "AI Manager not created"
		elif not ai_manager.ai_captain:
			success = false
			details = "AI Captain not initialized"
		elif not ai_manager.ai_engineer:
			success = false
			details = "AI Engineer not initialized"
		elif not ai_manager.ai_first_mate:
			success = false
			details = "AI First Mate not initialized"
		elif not ai_manager.ai_radio_operator:
			success = false
			details = "AI Radio Operator not initialized"
		else:
			details = "All AI stations initialized successfully"
		
	except:
		success = false
		details = "Exception during AI manager initialization"
	
	_add_test_result(test_name, success, details)

func _test_captain_movement_decisions() -> void:
	var test_name = "Captain Movement Decisions"
	var success = true
	var details = ""
	
	try:
		if not ai_manager or not ai_manager.ai_captain:
			success = false
			details = "AI Captain not available"
		else:
			var captain = ai_manager.ai_captain
			
			# Test easy difficulty movement
			captain.difficulty = AIProfile.Difficulty.EASY
			var current_pos = Vector2i(5, 5)
			var direction = captain._get_easy_movement_direction(current_pos)
			
			if direction == "":
				success = false
				details = "Easy movement returned empty direction"
			elif not direction in ["NORTH", "SOUTH", "EAST", "WEST"]:
				success = false
				details = "Invalid direction returned: " + direction
			else:
				details = "Movement decision test passed, direction: " + direction
		
	except:
		success = false
		details = "Exception during captain movement test"
	
	_add_test_result(test_name, success, details)

func _test_engineer_damage_tracking() -> void:
	var test_name = "Engineer Damage Tracking"
	var success = true
	var details = ""
	
	try:
		if not ai_manager or not ai_manager.ai_engineer:
			success = false
			details = "AI Engineer not available"
		else:
			var engineer = ai_manager.ai_engineer
			
			# Test damage marking
			engineer._mark_damage_easy("NORTH")
			var damage_status = engineer.get_damage_status()
			
			var north_damage = damage_status.get("north", [])
			var has_damage = false
			for damaged in north_damage:
				if damaged:
					has_damage = true
					break
			
			if not has_damage:
				success = false
				details = "Damage marking failed"
			else:
				details = "Damage tracking test passed"
		
	except:
		success = false
		details = "Exception during engineer damage test"
	
	_add_test_result(test_name, success, details)

func _test_first_mate_equipment_charging() -> void:
	var test_name = "First Mate Equipment Charging"
	var success = true
	var details = ""
	
	try:
		if not ai_manager or not ai_manager.ai_first_mate:
			success = false
			details = "AI First Mate not available"
		else:
			var first_mate = ai_manager.ai_first_mate
			
			# Test equipment charging
			var initial_torpedo = ai_manager.equipment_current.get("torpedo", 0)
			first_mate._charge_equipment("torpedo")
			var new_torpedo = ai_manager.equipment_current.get("torpedo", 0)
			
			if new_torpedo <= initial_torpedo:
				success = false
				details = "Equipment charging failed"
			else:
				details = "Equipment charging test passed"
		
	except:
		success = false
		details = "Exception during first mate equipment test"
	
	_add_test_result(test_name, success, details)

func _test_radio_operator_tracking() -> void:
	var test_name = "Radio Operator Tracking"
	var success = true
	var details = ""
	
	try:
		if not ai_manager or not ai_manager.ai_radio_operator:
			success = false
			details = "AI Radio Operator not available"
		else:
			var radio_op = ai_manager.ai_radio_operator
			
			# Test enemy movement tracking
			radio_op.track_enemy_movement("NORTH")
			radio_op.track_enemy_movement("EAST")
			
			if radio_op.user_positions.size() != 2:
				success = false
				details = "Movement tracking failed"
			elif radio_op.user_positions[0] != "NORTH" or radio_op.user_positions[1] != "EAST":
				success = false
				details = "Movement tracking incorrect"
			else:
				details = "Radio operator tracking test passed"
		
	except:
		success = false
		details = "Exception during radio operator test"
	
	_add_test_result(test_name, success, details)

func _test_ai_integration_with_gamestate() -> void:
	var test_name = "AI Integration with GameState"
	var success = true
	var details = ""
	
	try:
		if not GameState:
			success = false
			details = "GameState not available"
		else:
			# Test AI enabling
			GameState.set_ai_enabled(true)
			
			if not GameState.ai_enabled:
				success = false
				details = "AI enabling failed"
			else:
				# Test AI status retrieval
				var ai_status = GameState.get_ai_status()
				if ai_status.is_empty():
					success = false
					details = "AI status retrieval failed"
				else:
					details = "GameState integration test passed"
		
	except:
		success = false
		details = "Exception during GameState integration test"
	
	_add_test_result(test_name, success, details)

func _add_test_result(test_name: String, success: bool, details: String) -> void:
	test_results.append({
		"name": test_name,
		"success": success,
		"details": details
	})

func _print_test_results() -> void:
	print("\n=== AI Integration Test Results ===")
	
	var passed = 0
	var total = test_results.size()
	
	for result in test_results:
		var status = "PASS" if result.success else "FAIL"
		print("[%s] %s: %s" % [status, result.name, result.details])
		if result.success:
			passed += 1
	
	print("\nSummary: %d/%d tests passed (%.1f%%)" % [passed, total, (float(passed) / float(total)) * 100.0])
	
	if passed == total:
		print("🎉 All tests passed! AI integration is working correctly.")
	else:
		print("⚠️  Some tests failed. Check the details above.")

# Manual test functions that can be called from the AI config panel
func test_ai_movement() -> void:
	if ai_manager and ai_manager.ai_captain:
		ai_manager.ai_captain.start_movement_decision()
		print("AI movement test triggered")

func test_ai_surface() -> void:
	if ai_manager:
		ai_manager._execute_surface()
		print("AI surface test triggered")

func test_equipment_charging() -> void:
	if ai_manager and ai_manager.ai_first_mate:
		ai_manager.ai_first_mate.start_charging_work(3)
		print("Equipment charging test triggered")

func get_detailed_ai_status() -> Dictionary:
	if not ai_manager:
		return {"error": "AI Manager not available"}
	
	return {
		"captain": {
			"difficulty": ai_manager.ai_captain.difficulty if ai_manager.ai_captain else "N/A",
			"position": ai_manager.get_current_position(),
			"enemy_certainty": ai_manager.ai_captain.enemy_certainty if ai_manager.ai_captain else 0.0
		},
		"engineer": {
			"difficulty": ai_manager.ai_engineer.difficulty if ai_manager.ai_engineer else "N/A",
			"should_surface": ai_manager.ai_engineer.should_surface if ai_manager.ai_engineer else false,
			"damage_status": ai_manager.ai_engineer.get_damage_status() if ai_manager.ai_engineer else {}
		},
		"first_mate": {
			"difficulty": ai_manager.ai_first_mate.difficulty if ai_manager.ai_first_mate else "N/A",
			"equipment": ai_manager.equipment_current.duplicate()
		},
		"radio_operator": {
			"difficulty": ai_manager.ai_radio_operator.difficulty if ai_manager.ai_radio_operator else "N/A",
			"certainty": ai_manager.ai_radio_operator.get_certainty() if ai_manager.ai_radio_operator else 0.0,
			"tracked_moves": ai_manager.ai_radio_operator.user_positions.size() if ai_manager.ai_radio_operator else 0
		}
	}
