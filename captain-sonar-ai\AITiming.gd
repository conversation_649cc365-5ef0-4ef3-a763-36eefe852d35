extends Node
class_name AITiming

# AI Timing and Synchronization System
# Replaces C++ threading with Godot-appropriate timing mechanisms

signal station_ready(station_type: AIProfile.StationType)
signal turn_completed()
signal timing_synchronized()

var ai_manager

# Timing control
var turn_active: bool = false
var stations_processing: Dictionary = {}
var response_delays: Dictionary = {}
var equipment_timers: Dictionary = {}

# Synchronization
var movement_done: bool = false
var ai_called_stop: bool = true
var processing_queue: Array = []

func setup(manager: AIManager) -> void:
	ai_manager = manager
	_initialize_timing_system()

func _initialize_timing_system() -> void:
	# Initialize station timing
	stations_processing = {
		AIProfile.StationType.CAPTAIN: false,
		AIProfile.StationType.ENGINEER: false,
		AIProfile.StationType.FIRST_MATE: false,
		AIProfile.StationType.RADIO_OPERATOR: false
	}
	
	# Initialize response delays (matching C++ response times)
	response_delays = {
		AIProfile.StationType.CAPTAIN: 20,
		AIProfile.StationType.ENGINEER: 20,
		AIProfile.StationType.FIRST_MATE: 20,
		AIProfile.StationType.RADIO_OPERATOR: 20
	}
	
	# Initialize equipment charging timers
	equipment_timers = {
		"mines": 0.0,
		"torpedo": 0.0,
		"drones": 0.0,
		"sonar": 0.0,
		"silence": 0.0,
		"scenario": 0.0
	}

func start_ai_turn() -> void:
	if turn_active:
		return
	
	turn_active = true
	movement_done = false
	ai_called_stop = false
	
	# Start all stations with proper timing
	_start_station_processing()

func _start_station_processing() -> void:
	# Start stations in order with delays (matching C++ threading)
	
	# Captain starts first (movement decision)
	if ai_manager and ai_manager.ai_captain:
		_start_station_with_delay(AIProfile.StationType.CAPTAIN, 0.0)
	
	# Engineer starts slightly after captain
	if ai_manager and ai_manager.ai_engineer:
		_start_station_with_delay(AIProfile.StationType.ENGINEER, 0.1)
	
	# First mate starts with random delay (matching C++ random timing)
	if ai_manager and ai_manager.ai_first_mate:
		var delay = randf() * 0.3 + 0.1  # 0.1-0.4 seconds
		_start_station_with_delay(AIProfile.StationType.FIRST_MATE, delay)
	
	# Radio operator starts last
	if ai_manager and ai_manager.ai_radio_operator:
		_start_station_with_delay(AIProfile.StationType.RADIO_OPERATOR, 0.2)

func _start_station_with_delay(station_type: AIProfile.StationType, delay: float) -> void:
	await get_tree().create_timer(delay).timeout
	
	if not turn_active:
		return
	
	stations_processing[station_type] = true
	
	# Add response time delay (matching C++ chrono delays)
	var response_time = response_delays[station_type]
	var random_factor = randf() * 0.3 + 0.1  # Add randomness like C++
	var total_delay = (response_time + random_factor) / 100.0  # Convert to seconds
	
	await get_tree().create_timer(total_delay).timeout
	
	if not turn_active:
		return
	
	# Execute station logic
	_execute_station_logic(station_type)

func _execute_station_logic(station_type: AIProfile.StationType) -> void:
	if not ai_manager:
		return
	
	match station_type:
		AIProfile.StationType.CAPTAIN:
			if ai_manager.ai_captain and not ai_manager.ai_captain.is_done():
				ai_manager.ai_captain.process_station()
		
		AIProfile.StationType.ENGINEER:
			if ai_manager.ai_engineer and not ai_manager.ai_engineer.is_done():
				ai_manager.ai_engineer.process_station()
		
		AIProfile.StationType.FIRST_MATE:
			if ai_manager.ai_first_mate and not ai_manager.ai_first_mate.is_done():
				ai_manager.ai_first_mate.process_station()
		
		AIProfile.StationType.RADIO_OPERATOR:
			if ai_manager.ai_radio_operator and not ai_manager.ai_radio_operator.is_done():
				ai_manager.ai_radio_operator.process_station()
	
	# Mark station as completed
	stations_processing[station_type] = false
	emit_signal("station_ready", station_type)
	
	# Check if all stations are done
	_check_turn_completion()

func _check_turn_completion() -> void:
	# Check if all stations have completed their processing
	var all_done = true
	for station_type in stations_processing:
		if stations_processing[station_type]:
			all_done = false
			break
	
	if all_done:
		_complete_turn()

func _complete_turn() -> void:
	turn_active = false
	movement_done = true
	
	# Process equipment charging
	_update_equipment_charging()
	
	emit_signal("turn_completed")
	emit_signal("timing_synchronized")

func _update_equipment_charging() -> void:
	# Update equipment charging with proper timing
	if not ai_manager:
		return
	
	var delta_time = get_process_delta_time()
	
	for equipment_name in equipment_timers:
		equipment_timers[equipment_name] += delta_time
		
		# Check if equipment should charge (based on first mate efficiency)
		var charge_rate = _get_equipment_charge_rate(equipment_name)
		
		if equipment_timers[equipment_name] >= charge_rate:
			_charge_equipment(equipment_name)
			equipment_timers[equipment_name] = 0.0

func _get_equipment_charge_rate(equipment_name: String) -> float:
	# Get charging rate based on first mate difficulty and efficiency
	var base_rate = 2.0  # Base 2 seconds per charge
	
	if ai_manager and ai_manager.ai_first_mate:
		match ai_manager.ai_first_mate.difficulty:
			AIProfile.Difficulty.EASY:
				base_rate = 3.0  # Slower charging
			AIProfile.Difficulty.NORMAL:
				base_rate = 2.0  # Normal charging
			AIProfile.Difficulty.HARD:
				base_rate = 1.5  # Faster charging
	
	# Equipment-specific modifiers
	match equipment_name:
		"torpedo":
			base_rate *= 1.5  # Torpedoes charge slower
		"silence":
			base_rate *= 0.8  # Silence charges faster
		"mines":
			base_rate *= 1.2  # Mines charge slightly slower
	
	return base_rate

func _charge_equipment(equipment_name: String) -> void:
	if not ai_manager:
		return
	
	var current = ai_manager.equipment_current.get(equipment_name, 0)
	var maximum = ai_manager.equipment_max.get(equipment_name, 1)
	
	if current < maximum:
		ai_manager.equipment_current[equipment_name] = current + 1
		
		if ai_manager.debug_mode:
			print("Timing: I'm doing Equipment charged: ", equipment_name, " (", current + 1, "/", maximum, ")")

# Synchronization functions (matching C++ synchronization)
func wait_for_movement_done() -> void:
	while not movement_done and turn_active:
		await get_tree().process_frame

func set_movement_done(done: bool) -> void:
	movement_done = done
	if done:
		ai_called_stop = true

func is_station_processing(station_type: AIProfile.StationType) -> bool:
	return stations_processing.get(station_type, false)

func get_station_response_time(station_type: AIProfile.StationType) -> int:
	return response_delays.get(station_type, 20)

func set_station_response_time(station_type: AIProfile.StationType, time_ms: int) -> void:
	response_delays[station_type] = time_ms

# Equipment timing functions
func get_equipment_charge_progress(equipment_name: String) -> float:
	var timer = equipment_timers.get(equipment_name, 0.0)
	var rate = _get_equipment_charge_rate(equipment_name)
	return timer / rate if rate > 0 else 0.0

func reset_equipment_timer(equipment_name: String) -> void:
	equipment_timers[equipment_name] = 0.0

func pause_all_timers() -> void:
	# Pause all timing when AI surfaces or game is paused
	turn_active = false
	for station_type in stations_processing:
		stations_processing[station_type] = false

func resume_all_timers() -> void:
	# Resume timing after pause
	if not turn_active:
		start_ai_turn()

# Debug functions
func get_timing_status() -> Dictionary:
	return {
		"turn_active": turn_active,
		"movement_done": movement_done,
		"stations_processing": stations_processing.duplicate(),
		"equipment_timers": equipment_timers.duplicate(),
		"response_delays": response_delays.duplicate()
	}

func _process(delta: float) -> void:
	# Continuous equipment charging update
	if turn_active:
		_update_equipment_charging()

# Station-specific timing functions (matching C++ station timing)
func captain_timing_delay() -> float:
	# Captain uses random delay for decision making
	return (randf() * 30 + 1 + response_delays[AIProfile.StationType.CAPTAIN]) / 100.0

func engineer_timing_delay() -> float:
	# Engineer uses different delay based on difficulty
	var base_delay = response_delays[AIProfile.StationType.ENGINEER]
	if ai_manager and ai_manager.ai_engineer:
		match ai_manager.ai_engineer.difficulty:
			AIProfile.Difficulty.NORMAL:
				base_delay += randf() * 30  # More variable timing for normal
	
	return (base_delay + 1) / 100.0

func first_mate_timing_delay() -> float:
	# First mate uses random delay with wider range
	return (randf() * 30 + 1 + response_delays[AIProfile.StationType.FIRST_MATE]) / 100.0

func radio_operator_timing_delay() -> float:
	# Radio operator uses minimal delay
	return (randf() * 1 + response_delays[AIProfile.StationType.RADIO_OPERATOR]) / 100.0
