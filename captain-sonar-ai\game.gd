extends Control

# Captain Station - Main game interface for the captain
# Shows the game board with player submarine and enemy submarine (only when surfaced)

@onready var tile_map_layer = $TileMap/TileMapLayer
@onready var player_submarine = $PlayerSubmarine
@onready var enemy_submarine = $EnemySubmarine
@onready var movement_buttons = $MovementPanel
@onready var status_label = $StatusLabel

const MAP_WIDTH = 15
const MAP_HEIGHT = 15
const TILE_SIZE = 128  # Matches the tile_size in the TileSet resource
const TILE_ATLAS_COORD = Vector2i(0, 0)
const WATER_SOURCE_ID = 0
const ISLAND_SOURCE_ID = 1

var map_data = []
var player_team_id = "alpha"  # This captain controls the alpha team

func _ready():
	_generate_map_data()
	_render_map_from_data()
	_setup_movement_controls()

	# Connect to GameState (should be available as autoload)
	if GameState:
		GameState.initialize_game(map_data)
		if not GameState.is_connected("state_updated", _on_state_updated):
			GameState.connect("state_updated", _on_state_updated)
		if not GameState.is_connected("ai_action_performed", _on_ai_action_performed):
			GameState.connect("ai_action_performed", _on_ai_action_performed)
		_update_submarine_positions()

		# Enable AI for enemy team
		GameState.set_ai_enabled(true)
	else:
		print("Warning: GameState not found as autoload")

func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://player_selection.tscn")

# Setup movement control buttons
func _setup_movement_controls():
	# Movement controls will be handled by UI buttons
	pass

# Handle submarine state updates from GameState
func _on_state_updated(team_id: String) -> void:
	_update_submarine_positions()
	_update_status_display()

# Update submarine positions on the map
func _update_submarine_positions() -> void:
	if not GameState or not GameState.teams:
		return

	if not player_submarine or not enemy_submarine:
		print("Warning: Submarine sprites not found")
		return

	# Update player submarine (always visible)
	if GameState.teams.has(player_team_id):
		var player_state: SubmarineState = GameState.teams[player_team_id]
		# Convert grid position to world position, accounting for TileMap offset and scale
		# TileMap is at position (183, 60) with scale (0.25, 0.25)
		var tilemap_offset = Vector2(183, 60)
		var tilemap_scale = Vector2(0.25, 0.25)
		var scaled_tile_size = TILE_SIZE * tilemap_scale.x
		var player_world_pos = Vector2(player_state.position) * scaled_tile_size + tilemap_offset + Vector2(scaled_tile_size/1.5, scaled_tile_size/1.5)
		player_submarine.position = player_world_pos
		player_submarine.visible = true

		# Update rotation based on heading
		player_submarine.rotation_degrees = _direction_to_rotation(player_state.heading)

	# Update enemy submarine (only visible when surfaced)
	var enemy_team_id = "bravo" if player_team_id == "alpha" else "alpha"
	if GameState.teams.has(enemy_team_id):
		var enemy_state: SubmarineState = GameState.teams[enemy_team_id]
		# Convert grid position to world position, accounting for TileMap offset and scale
		var tilemap_offset = Vector2(183, 60)
		var tilemap_scale = Vector2(0.25, 0.25)
		var scaled_tile_size = TILE_SIZE * tilemap_scale.x
		var enemy_world_pos = Vector2(enemy_state.position) * scaled_tile_size + tilemap_offset + Vector2(scaled_tile_size/2, scaled_tile_size/2)
		enemy_submarine.position = enemy_world_pos

		# Only show enemy submarine when surfaced
		enemy_submarine.visible = enemy_state.is_surfaced

		if enemy_state.is_surfaced:
			enemy_submarine.rotation_degrees = _direction_to_rotation(enemy_state.heading)

# Convert direction string to rotation degrees
func _direction_to_rotation(direction: String) -> float:
	match direction:
		"NORTH":
			return -90.0
		"SOUTH":
			return 90.0
		"EAST":
			return 0.0
		"WEST":
			return 180.0
		_:
			return 0.0

# Update status display with current submarine info
func _update_status_display() -> void:
	if not GameState or not GameState.teams.has(player_team_id):
		return

	var player_state: SubmarineState = GameState.teams[player_team_id]
	var enemy_team_id = "bravo" if player_team_id == "alpha" else "alpha"
	var enemy_visible = ""

	if GameState.teams.has(enemy_team_id):
		var enemy_state: SubmarineState = GameState.teams[enemy_team_id]
		enemy_visible = " | Enemy: %s" % ("Surfaced" if enemy_state.is_surfaced else "Submerged")

	var status_text = "Position: (%d, %d) | Heading: %s | Health: %d | Heat: %.1f%s" % [
		player_state.position.x,
		player_state.position.y,
		player_state.heading,
		player_state.health,
		player_state.heat,
		enemy_visible
	]

	if status_label:
		status_label.text = status_text

# Movement command functions
func _move_north() -> void:
	if GameState:
		var old_position = GameState.teams[player_team_id].position if GameState.teams.has(player_team_id) else Vector2i.ZERO
		GameState.process_captain_move(player_team_id, "NORTH")
		_check_movement_result(old_position, "NORTH")
		_trigger_ai_turn()

func _move_south() -> void:
	if GameState:
		var old_position = GameState.teams[player_team_id].position if GameState.teams.has(player_team_id) else Vector2i.ZERO
		GameState.process_captain_move(player_team_id, "SOUTH")
		_check_movement_result(old_position, "SOUTH")
		_trigger_ai_turn()

func _move_east() -> void:
	if GameState:
		var old_position = GameState.teams[player_team_id].position if GameState.teams.has(player_team_id) else Vector2i.ZERO
		GameState.process_captain_move(player_team_id, "EAST")
		_check_movement_result(old_position, "EAST")
		_trigger_ai_turn()

func _move_west() -> void:
	if GameState:
		var old_position = GameState.teams[player_team_id].position if GameState.teams.has(player_team_id) else Vector2i.ZERO
		GameState.process_captain_move(player_team_id, "WEST")
		_check_movement_result(old_position, "WEST")
		_trigger_ai_turn()

# Check if movement was successful and provide feedback
func _check_movement_result(old_position: Vector2i, direction: String) -> void:
	if GameState and GameState.teams.has(player_team_id):
		var new_position = GameState.teams[player_team_id].position
		if old_position == new_position:
			# Movement was blocked
			print("Movement blocked: Cannot move %s" % direction)

# Test functions for enemy submarine surfacing
func _surface_enemy() -> void:
	if GameState:
		var enemy_team_id = "bravo" if player_team_id == "alpha" else "alpha"
		if GameState.teams.has(enemy_team_id):
			var enemy_state: SubmarineState = GameState.teams[enemy_team_id]
			enemy_state.is_surfaced = true
			GameState.emit_signal("state_updated", enemy_team_id)

func _submerge_enemy() -> void:
	if GameState:
		var enemy_team_id = "bravo" if player_team_id == "alpha" else "alpha"
		if GameState.teams.has(enemy_team_id):
			var enemy_state: SubmarineState = GameState.teams[enemy_team_id]
			enemy_state.is_surfaced = false
			GameState.emit_signal("state_updated", enemy_team_id)

# Fills map_data array with true (island) or false (water).
# Currently makes a border around the map
func _generate_map_data():
	for x in MAP_WIDTH:
		map_data.append([]) # Add a new column
		for y in MAP_HEIGHT:
			map_data[x].append(false) # Fill with water by default

# This function reads map_data and places the correct tiles.
func _render_map_from_data():
	tile_map_layer.clear()

	for x in MAP_WIDTH:
		for y in MAP_HEIGHT:
			if map_data[x][y] == true:
				# It's an island! Use the ISLAND source and the TILE atlas coord.
				tile_map_layer.set_cell(Vector2i(x, y), ISLAND_SOURCE_ID, TILE_ATLAS_COORD)
			else:
				# It's water. Use the WATER source and the TILE atlas coord.
				tile_map_layer.set_cell(Vector2i(x, y), WATER_SOURCE_ID, TILE_ATLAS_COORD)

# AI Integration Functions
func _trigger_ai_turn() -> void:
	# Trigger AI to make its turn after player moves
	print("Game: I'm doing _trigger_ai_turn called")
	if GameState:
		print("Game: I'm doing GameState exists, waiting 0.5 seconds")
		# Add a small delay to make AI moves visible
		await get_tree().create_timer(0.5).timeout
		print("Game: I'm doing calling GameState.trigger_ai_turn()")

		GameState.trigger_ai_turn()
		print("Game: I'm doing GameState.trigger_ai_turn() completed")

	else:
		print("Game: I'm doing GameState not found")

func _on_ai_action_performed(team_id: String, station_type: int, action: String) -> void:
	# Handle AI actions
	print("AI Team ", team_id, " - Station ", station_type, ": ", action)

	# You can add visual feedback here for AI actions
	match station_type:
		0:  # CAPTAIN
			# AI captain moved - already handled by movement system
			pass
		1:  # ENGINEER
			# AI engineer completed repairs or surfacing
			if action.contains("surface"):
				print("AI is surfacing!")
		2:  # FIRST_MATE
			# AI first mate charged equipment
			if action.contains("ready"):
				print("AI equipment ready: ", action)
		3:  # RADIO_OPERATOR
			# AI radio operator gathered intelligence
			print("AI radio intelligence: ", action)
