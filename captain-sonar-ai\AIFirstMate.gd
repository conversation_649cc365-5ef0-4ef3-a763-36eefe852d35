extends Node
class_name AI<PERSON>irstMate

# AI First Mate Station - Manages weapons and equipment charging
# Based on the C++ First Mate AI implementation

signal equipment_ready(equipment_name: String)
signal charging_priority_changed(equipment_name: String, priority: int)

var ai_manager
var difficulty: AIProfile.Difficulty = AIProfile.Difficulty.NORMAL
var response_time: int = 20
var is_station_done: bool = true
var game_over: bool = false

# First mate tracking variables (matching C++ implementation)
var first_mate_list_made: bool = false
var first_mate_uses: int = 0
var first_mate_list: Array[int] = [0, 0, 0, 0, 0]  # Equipment charging priority list
var slot_position: int = 0
var failed_tries: int = 0

# Equipment types (matching C++ equipment struct indices)
enum EquipmentType {
	MINES = 0,
	TORPEDO = 1,
	DRONES = 2,
	SONAR = 3,
	SILENCE = 4,
	SCENARIO = 5
}

# Personality-based charging priorities (matching C++ exactly)
var personality_priorities: Dictionary = {
	1: [EquipmentType.MINES, EquipmentType.TORPEDO, EquipmentType.SILENCE, EquipmentType.DRONES, EquipmentType.SONAR],
	2: [EquipmentType.DRONES, EquipmentType.SONAR, EquipmentType.TORPEDO, EquipmentType.SILENCE, EquipmentType.MINES],
	3: [EquipmentType.DRONES, EquipmentType.SILENCE, EquipmentType.TORPEDO, EquipmentType.SONAR, EquipmentType.MINES]
}

# Equipment struct matching C++ implementation
var equipment_structs: Dictionary = {
	"mines": {"max": 3, "current": 0},
	"torpedo": {"max": 4, "current": 0},
	"drones": {"max": 3, "current": 0},
	"sonar": {"max": 3, "current": 0},
	"silence": {"max": 6, "current": 0},
	"scenario": {"max": 1, "current": 0}
}

func setup(manager: AIManager, diff: AIProfile.Difficulty, response_ms: int) -> void:
	ai_manager = manager
	difficulty = diff
	response_time = response_ms

	# Sync equipment structs with AI manager
	if ai_manager:
		for equipment_name in equipment_structs:
			if ai_manager.equipment_max.has(equipment_name):
				equipment_structs[equipment_name]["max"] = ai_manager.equipment_max[equipment_name]
			if ai_manager.equipment_current.has(equipment_name):
				equipment_structs[equipment_name]["current"] = ai_manager.equipment_current[equipment_name]

func is_done() -> bool:
	return is_station_done

func set_game_over(is_over: bool) -> void:
	game_over = is_over

func process_station() -> void:
	if game_over or is_station_done:
		return

	is_station_done = false

	# Process charging work immediately
	match difficulty:
		AIProfile.Difficulty.EASY:
			_process_easy_difficulty()
		AIProfile.Difficulty.NORMAL:
			_process_normal_difficulty()
		AIProfile.Difficulty.HARD:
			_process_hard_difficulty()

	# Add response delay after processing (reduced randomness for better performance)
	var delay_time = (randf() * 0.2 + response_time) / 100.0
	await get_tree().create_timer(delay_time).timeout

	if first_mate_uses == 0:
		is_station_done = true

func _process_easy_difficulty() -> void:
	# Easy first mate - random equipment charging (matching C++ exactly)
	if first_mate_uses > 0:
		var equipment_type = randi() % 6  # Random equipment selection (0-5)
		var equipment_name = _equipment_type_to_name(equipment_type)

		# Check each equipment type as in C++ code
		match equipment_type:
			0:  # mines
				if equipment_structs["mines"]["current"] < equipment_structs["mines"]["max"]:
					_charge_equipment("mines")
					first_mate_uses -= 1
			1:  # torpedo
				if equipment_structs["torpedo"]["current"] < equipment_structs["torpedo"]["max"]:
					_charge_equipment("torpedo")
					first_mate_uses -= 1
			2:  # drones
				if equipment_structs["drones"]["current"] < equipment_structs["drones"]["max"]:
					_charge_equipment("drones")
					first_mate_uses -= 1
			3:  # sonar
				if equipment_structs["sonar"]["current"] < equipment_structs["sonar"]["max"]:
					_charge_equipment("sonar")
					first_mate_uses -= 1
			4:  # silence
				if equipment_structs["silence"]["current"] < equipment_structs["silence"]["max"]:
					_charge_equipment("silence")
					first_mate_uses -= 1
			5:  # scenario
				if equipment_structs["scenario"]["current"] < equipment_structs["scenario"]["max"]:
					_charge_equipment("scenario")
					first_mate_uses -= 1

		# Handle failed attempts
		if first_mate_uses > 0:  # If we didn't charge anything
			failed_tries += 1
			if failed_tries >= 8:
				first_mate_uses = 0
				failed_tries = 0

func _process_normal_difficulty() -> void:
	# Normal first mate - priority-based equipment charging
	if not first_mate_list_made:
		_create_personality_list()
	
	if first_mate_uses > 0:
		var equipment_type = first_mate_list[slot_position]
		var equipment_name = _equipment_type_to_name(equipment_type)
		
		if _can_charge_equipment(equipment_name):
			_charge_equipment(equipment_name)
			first_mate_uses -= 1
			
			# Check if equipment is fully charged
			if _is_equipment_fully_charged(equipment_name):
				slot_position += 1
				if slot_position >= first_mate_list.size():
					slot_position = 0
					first_mate_list_made = false  # Reset for next cycle
		else:
			# Move to next equipment if current can't be charged
			slot_position += 1
			if slot_position >= first_mate_list.size():
				slot_position = 0
				first_mate_list_made = false

func _process_hard_difficulty() -> void:
	# Hard first mate - advanced priority management (placeholder)
	_process_normal_difficulty()

func _create_personality_list() -> void:
	# Create charging priority list based on captain difficulty and personality
	var personality = 1  # Default personality
	
	if ai_manager and ai_manager.ai_captain:
		var captain_difficulty = ai_manager.ai_captain.difficulty
		if captain_difficulty == AIProfile.Difficulty.EASY:
			personality = randi() % 3 + 1  # Random personality for easy captain
		else:
			# For normal+ captains, use more strategic personality
			personality = 2  # Balanced approach
	
	var source_list: Array
	if personality_priorities.has(personality):
		source_list = personality_priorities[personality]
	else:
		# Fallback to default priority
		source_list = [EquipmentType.TORPEDO, EquipmentType.DRONES, EquipmentType.SONAR, EquipmentType.MINES, EquipmentType.SILENCE]

	# Clear the old list and manually build a new, correctly typed list.
	first_mate_list.clear()
	for item in source_list:
		# Ensure each item is an integer and add it to the typed array.
		first_mate_list.append(int(item))
	
	first_mate_list_made = true
	slot_position = 0

func _equipment_type_to_name(equipment_type: int) -> String:
	match equipment_type:
		EquipmentType.MINES:
			return "mines"
		EquipmentType.TORPEDO:
			return "torpedo"
		EquipmentType.DRONES:
			return "drones"
		EquipmentType.SONAR:
			return "sonar"
		EquipmentType.SILENCE:
			return "silence"
		EquipmentType.SCENARIO:
			return "scenario"
		_:
			return "torpedo"

func _can_charge_equipment(equipment_name: String) -> bool:
	if not ai_manager:
		return false
	
	var current = ai_manager.equipment_current.get(equipment_name, 0)
	var maximum = ai_manager.equipment_max.get(equipment_name, 1)
	
	return current < maximum

func _is_equipment_fully_charged(equipment_name: String) -> bool:
	if not ai_manager:
		return false
	
	var current = ai_manager.equipment_current.get(equipment_name, 0)
	var maximum = ai_manager.equipment_max.get(equipment_name, 1)
	
	return current >= maximum

func _charge_equipment(equipment_name: String) -> void:
	if not ai_manager:
		return
	
	if ai_manager.equipment_current.has(equipment_name):
		ai_manager.equipment_current[equipment_name] += 1
		
		# Check if equipment is now fully charged
		if _is_equipment_fully_charged(equipment_name):
			emit_signal("equipment_ready", equipment_name)
			print("First Mate: I'm doing %s ready!" % equipment_name.capitalize())

func start_charging_work(uses: int) -> void:
	first_mate_uses = uses
	is_station_done = false
	failed_tries = 0
	process_station()

func get_equipment_status() -> Dictionary:
	if not ai_manager:
		return {}
	
	var status = {}
	for equipment_name in ai_manager.equipment_current:
		var current = ai_manager.equipment_current[equipment_name]
		var maximum = ai_manager.equipment_max[equipment_name]
		status[equipment_name] = {
			"current": current,
			"max": maximum,
			"percentage": (float(current) / float(maximum)) * 100.0 if maximum > 0 else 0.0,
			"ready": current >= maximum
		}
	
	return status

func get_charging_priority() -> Array[String]:
	var priority_names: Array[String] = []
	for equipment_type in first_mate_list:
		priority_names.append(_equipment_type_to_name(equipment_type))
	return priority_names

func set_charging_priority(priority_list: Array[String]) -> void:
	# Allow manual override of charging priority
	first_mate_list.clear()
	for equipment_name in priority_list:
		var equipment_type = _name_to_equipment_type(equipment_name)
		if equipment_type != -1:
			first_mate_list.append(equipment_type)
	
	first_mate_list_made = true
	slot_position = 0

func _name_to_equipment_type(equipment_name: String) -> int:
	# First, store the result of the function call in a variable
	var lower_name = equipment_name.to_lower()
	
	# Then, use the simple variable in the match statement
	match lower_name:
		"mines":
			return EquipmentType.MINES
		"torpedo":
			return EquipmentType.TORPEDO
		"drones":
			return EquipmentType.DRONES
		"sonar":
			return EquipmentType.SONAR
		"silence":
			return EquipmentType.SILENCE
		"scenario":
			return EquipmentType.SCENARIO
		_:
			return -1

func reset_charging_cycle() -> void:
	first_mate_list_made = false
	slot_position = 0
	failed_tries = 0

# Equipment usage functions (for captain to call)
func use_equipment(equipment_name: String) -> bool:
	if not ai_manager:
		return false
	
	if _is_equipment_fully_charged(equipment_name):
		ai_manager.equipment_current[equipment_name] = 0
		print("First Mate: I'm doing used %s!" % equipment_name.capitalize())
		return true
	
	return false

func can_use_equipment(equipment_name: String) -> bool:
	return _is_equipment_fully_charged(equipment_name)

# Combat decision making (for captain to query)
func recommend_weapon_usage(enemy_position: Vector2i, current_position: Vector2i, certainty: float) -> String:
	# Recommend which weapon to use based on situation
	var distance = current_position.distance_to(enemy_position)
	
	# High certainty and close range - use torpedo
	if certainty > 0.7 and distance <= 4 and can_use_equipment("torpedo"):
		return "torpedo"
	
	# Medium certainty - use drones for detection
	if certainty > 0.3 and certainty < 0.8 and can_use_equipment("drones"):
		return "drones"
	
	# Low certainty - use sonar
	if certainty < 0.5 and can_use_equipment("sonar"):
		return "sonar"
	
	# Defensive - lay mines
	if can_use_equipment("mines"):
		return "mines"
	
	return ""
