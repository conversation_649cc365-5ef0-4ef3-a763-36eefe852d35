[gd_scene load_steps=10 format=3 uid="uid://b1hpdws5k06wh"]

[ext_resource type="Script" uid="uid://d0sys4x07ive3" path="res://PlayerSelection.gd" id="1_player_sel"]

[sub_resource type="ButtonGroup" id="ButtonGroup_player_radio"]

[sub_resource type="ButtonGroup" id="ButtonGroup_player_captain"]

[sub_resource type="ButtonGroup" id="ButtonGroup_player_engineer"]

[sub_resource type="ButtonGroup" id="ButtonGroup_player_weapons"]

[sub_resource type="ButtonGroup" id="ButtonGroup_enemy_radio"]

[sub_resource type="ButtonGroup" id="ButtonGroup_enemy_captain"]

[sub_resource type="ButtonGroup" id="ButtonGroup_enemy_engineer"]

[sub_resource type="ButtonGroup" id="ButtonGroup_enemy_weapons"]

[node name="PlayerSelection" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_player_sel")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="ScrollContainer" type="ScrollContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 70.0
offset_right = -50.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="TitleLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
text = "Player Selection"
horizontal_alignment = 1

[node name="InstructionLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
text = "Choose your role and set AI difficulty for other stations"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="PlayerSubPanel" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="PlayerSubLabel" type="Label" parent="ScrollContainer/VBoxContainer/PlayerSubPanel"]
layout_mode = 2
text = "Your Submarine"
horizontal_alignment = 1

[node name="PlayerStations" type="VBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel"]
layout_mode = 2

[node name="RadioStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations"]
layout_mode = 2

[node name="RadioLabel" type="Label" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/RadioStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "Radio Operator:"
vertical_alignment = 1

[node name="RadioOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/RadioStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RadioHuman" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/RadioStation/RadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station - leaves station interface empty for manual control"
toggle_mode = true
button_group = SubResource("ButtonGroup_player_radio")
text = "Human"

[node name="RadioEasy" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/RadioStation/RadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_player_radio")
text = "Easy AI"

[node name="RadioNormal" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/RadioStation/RadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_radio")
text = "Normal AI"

[node name="RadioHard" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/RadioStation/RadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_radio")
text = "Hard AI"

[node name="CaptainStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations"]
layout_mode = 2

[node name="CaptainLabel" type="Label" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/CaptainStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "Captain:"
vertical_alignment = 1

[node name="CaptainOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/CaptainStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="CaptainHuman" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/CaptainStation/CaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station"
toggle_mode = true
button_group = SubResource("ButtonGroup_player_captain")
text = "Human"

[node name="CaptainEasy" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/CaptainStation/CaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_player_captain")
text = "Easy AI"

[node name="CaptainNormal" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/CaptainStation/CaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_captain")
text = "Normal AI"

[node name="CaptainHard" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/CaptainStation/CaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_captain")
text = "Hard AI"

[node name="EngineerStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations"]
layout_mode = 2

[node name="EngineerLabel" type="Label" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/EngineerStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "Engineer:"
vertical_alignment = 1

[node name="EngineerOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/EngineerStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EngineerHuman" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/EngineerStation/EngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station"
toggle_mode = true
button_group = SubResource("ButtonGroup_player_engineer")
text = "Human"

[node name="EngineerEasy" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/EngineerStation/EngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_player_engineer")
text = "Easy AI"

[node name="EngineerNormal" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/EngineerStation/EngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_engineer")
text = "Normal AI"

[node name="EngineerHard" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/EngineerStation/EngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_engineer")
text = "Hard AI"

[node name="WeaponsStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations"]
layout_mode = 2

[node name="WeaponsLabel" type="Label" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/WeaponsStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "First Mate (Weapons):"
vertical_alignment = 1

[node name="WeaponsOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/WeaponsStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="WeaponsHuman" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/WeaponsStation/WeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station"
toggle_mode = true
button_group = SubResource("ButtonGroup_player_weapons")
text = "Human"

[node name="WeaponsEasy" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/WeaponsStation/WeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_player_weapons")
text = "Easy AI"

[node name="WeaponsNormal" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/WeaponsStation/WeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_weapons")
text = "Normal AI"

[node name="WeaponsHard" type="Button" parent="ScrollContainer/VBoxContainer/PlayerSubPanel/PlayerStations/WeaponsStation/WeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_player_weapons")
text = "Hard AI"

[node name="HSeparator2" type="HSeparator" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="EnemySubPanel" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="EnemySubLabel" type="Label" parent="ScrollContainer/VBoxContainer/EnemySubPanel"]
layout_mode = 2
text = "Enemy Submarine"
horizontal_alignment = 1

[node name="EnemyStations" type="VBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel"]
layout_mode = 2

[node name="EnemyRadioStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations"]
layout_mode = 2

[node name="EnemyRadioLabel" type="Label" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyRadioStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "Radio Operator:"
vertical_alignment = 1

[node name="EnemyRadioOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyRadioStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EnemyRadioEasy" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyRadioStation/EnemyRadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_enemy_radio")
text = "Easy AI"

[node name="EnemyRadioNormal" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyRadioStation/EnemyRadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_radio")
text = "Normal AI"

[node name="EnemyRadioHard" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyRadioStation/EnemyRadioOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_radio")
text = "Hard AI"

[node name="EnemyCaptainStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations"]
layout_mode = 2

[node name="EnemyCaptainLabel" type="Label" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyCaptainStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "Captain:"
vertical_alignment = 1

[node name="EnemyCaptainOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyCaptainStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EnemyCaptainEasy" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyCaptainStation/EnemyCaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_enemy_captain")
text = "Easy AI"

[node name="EnemyCaptainNormal" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyCaptainStation/EnemyCaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_captain")
text = "Normal AI"

[node name="EnemyCaptainHard" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyCaptainStation/EnemyCaptainOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_captain")
text = "Hard AI"

[node name="EnemyEngineerStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations"]
layout_mode = 2

[node name="EnemyEngineerLabel" type="Label" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyEngineerStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "Engineer:"
vertical_alignment = 1

[node name="EnemyEngineerOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyEngineerStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EnemyEngineerEasy" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyEngineerStation/EnemyEngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_enemy_engineer")
text = "Easy AI"

[node name="EnemyEngineerNormal" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyEngineerStation/EnemyEngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_engineer")
text = "Normal AI"

[node name="EnemyEngineerHard" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyEngineerStation/EnemyEngineerOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_engineer")
text = "Hard AI"

[node name="EnemyWeaponsStation" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations"]
layout_mode = 2

[node name="EnemyWeaponsLabel" type="Label" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyWeaponsStation"]
layout_mode = 2
size_flags_horizontal = 3
text = "First Mate (Weapons):"
vertical_alignment = 1

[node name="EnemyWeaponsOptions" type="HBoxContainer" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyWeaponsStation"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EnemyWeaponsEasy" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyWeaponsStation/EnemyWeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_enemy_weapons")
text = "Easy AI"

[node name="EnemyWeaponsNormal" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyWeaponsStation/EnemyWeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_weapons")
text = "Normal AI"

[node name="EnemyWeaponsHard" type="Button" parent="ScrollContainer/VBoxContainer/EnemySubPanel/EnemyStations/EnemyWeaponsStation/EnemyWeaponsOptions"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
button_group = SubResource("ButtonGroup_enemy_weapons")
text = "Hard AI"

[node name="HSeparator3" type="HSeparator" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
text = "Select at least one Human station to continue"
horizontal_alignment = 1

[node name="StartGameButton" type="Button" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
disabled = true
text = "Start Game"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="ScrollContainer/VBoxContainer/StartGameButton" to="." method="_on_start_game_button_pressed"]
