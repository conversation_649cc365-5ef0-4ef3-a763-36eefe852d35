[editor_metadata]

executable_path="D:/SteamLibrary/steamapps/common/Godot Engine/godot.windows.opt.tools.64.exe"
use_advanced_connections=false

[recent_files]

scenes=["res://player_selection.tscn", "res://SpectatorView.tscn", "res://PathOverlay.tscn", "res://bridge.tscn", "res://radio_station.tscn", "res://engineer_station.tscn", "res://weapons_station.tscn", "res://EnemyAI.tscn", "res://player.tscn", "res://help.tscn"]
scripts=["res://AIPerformanceMonitor.gd", "@GlobalScope", "TextEdit", "res://AIEngineer.gd", "res://AIFirstMate.gd", "res://AIRadioOperator.gd", "VBoxContainer", "res://AICaptain.gd", "res://AIManager.gd", "res://SubmarineState.gd"]

[dialog_bounds]

create_new_node=Rect2(510, 190, 900, 700)
search_help=Rect2(480, 270, 960, 540)

[script_setup]

last_selected_language="GDScript"

[game_view]

embed_size_mode=0

[color_picker]

picker_shape=3
recent_presets=PackedColorArray(1, 1, 1, 1)

[quick_open_dialog]

last_mode=1
