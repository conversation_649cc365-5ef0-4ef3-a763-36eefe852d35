extends Control

# AI Configuration Panel - Allows setting up AI difficulty and behavior
# Provides testing tools and debug information

@onready var captain_difficulty_option = $VBoxContainer/CaptainPanel/CaptainDifficulty
@onready var engineer_difficulty_option = $VBoxContainer/EngineerPanel/EngineerDifficulty
@onready var first_mate_difficulty_option = $VBoxContainer/FirstMatePanel/FirstMateDifficulty
@onready var radio_difficulty_option = $VBoxContainer/RadioPanel/RadioDifficulty

@onready var ai_enabled_checkbox = $VBoxContainer/AIEnabledCheckbox
@onready var debug_mode_checkbox = $VBoxContainer/DebugModeCheckbox
@onready var status_label = $VBoxContainer/StatusLabel
@onready var test_buttons_panel = $VBoxContainer/TestButtonsPanel

@onready var back_button = $VBoxContainer/BackButton
@onready var apply_button = $VBoxContainer/ApplyButton

var ai_profile: AIProfile

func _ready() -> void:
	_setup_ui()
	_load_current_settings()

func _setup_ui() -> void:
	# Setup difficulty option buttons
	if captain_difficulty_option:
		captain_difficulty_option.clear()
		captain_difficulty_option.add_item("Easy", AIProfile.Difficulty.EASY)
		captain_difficulty_option.add_item("Normal", AIProfile.Difficulty.NORMAL)
		captain_difficulty_option.add_item("Hard", AIProfile.Difficulty.HARD)
	
	if engineer_difficulty_option:
		engineer_difficulty_option.clear()
		engineer_difficulty_option.add_item("Easy", AIProfile.Difficulty.EASY)
		engineer_difficulty_option.add_item("Normal", AIProfile.Difficulty.NORMAL)
		engineer_difficulty_option.add_item("Hard", AIProfile.Difficulty.HARD)
	
	if first_mate_difficulty_option:
		first_mate_difficulty_option.clear()
		first_mate_difficulty_option.add_item("Easy", AIProfile.Difficulty.EASY)
		first_mate_difficulty_option.add_item("Normal", AIProfile.Difficulty.NORMAL)
		first_mate_difficulty_option.add_item("Hard", AIProfile.Difficulty.HARD)
	
	if radio_difficulty_option:
		radio_difficulty_option.clear()
		radio_difficulty_option.add_item("Easy", AIProfile.Difficulty.EASY)
		radio_difficulty_option.add_item("Normal", AIProfile.Difficulty.NORMAL)
		radio_difficulty_option.add_item("Hard", AIProfile.Difficulty.HARD)
	
	# Connect signals
	if apply_button:
		apply_button.pressed.connect(_on_apply_pressed)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func _load_current_settings() -> void:
	# Load current AI settings from GameState
	if not ai_profile:
		ai_profile = AIProfile.new()
	
	# Set UI to current values
	if captain_difficulty_option:
		captain_difficulty_option.selected = ai_profile.captain_difficulty - 1
	if engineer_difficulty_option:
		engineer_difficulty_option.selected = ai_profile.engineer_difficulty - 1
	if first_mate_difficulty_option:
		first_mate_difficulty_option.selected = ai_profile.first_mate_difficulty - 1
	if radio_difficulty_option:
		radio_difficulty_option.selected = ai_profile.radio_operator_difficulty - 1
	
	if ai_enabled_checkbox:
		ai_enabled_checkbox.button_pressed = GameState.ai_enabled if GameState else true
	
	_update_status_display()

func _update_status_display() -> void:
	if not status_label:
		return
	
	var status_text = "AI Configuration Status:\n"
	
	if GameState and GameState.ai_manager:
		var ai_status = GameState.get_ai_status()
		status_text += "AI Enabled: %s\n" % str(ai_status.get("enabled", false))
		status_text += "AI Team: %s\n" % ai_status.get("team_id", "none")
		status_text += "AI Health: %d\n" % ai_status.get("health", 0)
		status_text += "AI Surfaced: %s\n" % str(ai_status.get("surfaced", false))
				status_text += "Radio Certainty: %.1f%%
" % (ai_status.get("radio_certainty", 0.0) * 100)
		
		if GameState.ai_manager.ai_radio_operator:
			status_text += "Radio Operator Difficulty: %s
" % ai_profile.difficulty_to_string(GameState.ai_manager.ai_radio_operator.difficulty)
			status_text += "Radio Operator Is Done: %s
" % str(GameState.ai_manager.ai_radio_operator.is_done())
			status_text += "Radio Operator Estimated Position: %s
" % str(GameState.ai_manager.ai_radio_operator.get_estimated_position())
		else:
			status_text += "Radio Operator: Not Initialized

		
		var equipment = ai_status.get("equipment", {})
		status_text += "\nEquipment Status:\n"
		for eq_name in equipment:
			var current = equipment[eq_name]
			var max_val = ai_profile.get("max_" + eq_name, 1) if ai_profile else 1
			status_text += "- %s: %d/%d\n" % [eq_name.capitalize(), current, max_val]
	else:
		status_text += "AI Not Active\n"
	
	status_label.text = status_text

func _on_apply_pressed() -> void:
	# Apply the selected settings
	if not ai_profile:
		ai_profile = AIProfile.new()
	
	# Update AI profile with selected difficulties
	if captain_difficulty_option:
		ai_profile.captain_difficulty = captain_difficulty_option.get_item_id(captain_difficulty_option.selected)
	if engineer_difficulty_option:
		ai_profile.engineer_difficulty = engineer_difficulty_option.get_item_id(engineer_difficulty_option.selected)
	if first_mate_difficulty_option:
		ai_profile.first_mate_difficulty = first_mate_difficulty_option.get_item_id(first_mate_difficulty_option.selected)
	if radio_difficulty_option:
		ai_profile.radio_operator_difficulty = radio_difficulty_option.get_item_id(radio_difficulty_option.selected)
	
	# Apply to GameState
	if GameState:
		if ai_enabled_checkbox:
			GameState.set_ai_enabled(ai_enabled_checkbox.button_pressed)
		
		if GameState.ai_manager:
			GameState.ai_manager.ai_profile = ai_profile
			# Reconfigure AI stations with new difficulties
			if GameState.ai_manager.ai_captain:
				GameState.ai_manager.ai_captain.difficulty = ai_profile.captain_difficulty
			if GameState.ai_manager.ai_engineer:
				GameState.ai_manager.ai_engineer.difficulty = ai_profile.engineer_difficulty
			if GameState.ai_manager.ai_first_mate:
				GameState.ai_manager.ai_first_mate.difficulty = ai_profile.first_mate_difficulty
			if GameState.ai_manager.ai_radio_operator:
				GameState.ai_manager.ai_radio_operator.difficulty = ai_profile.radio_operator_difficulty
	
	_update_status_display()
	print("AI configuration applied successfully!")

func _on_back_pressed() -> void:
	get_tree().change_scene_to_file("res://main_menu.tscn")

# Test functions
func _on_test_ai_movement_pressed() -> void:
	if GameState and GameState.ai_manager:
		print("Testing AI movement...")
		GameState.trigger_ai_turn()

func _on_test_ai_surface_pressed() -> void:
	if GameState and GameState.ai_manager:
		print("Testing AI surface...")
		GameState.ai_manager._execute_surface()

func _on_test_equipment_charge_pressed() -> void:
	if GameState and GameState.ai_manager and GameState.ai_manager.ai_first_mate:
		print("Testing equipment charging...")
		GameState.ai_manager.ai_first_mate.start_charging_work(5)

func _on_reset_ai_pressed() -> void:
	if GameState:
		print("Resetting AI...")
		GameState.set_ai_enabled(false)
		await get_tree().create_timer(0.1).timeout
		GameState.set_ai_enabled(true)
		_update_status_display()

# Debug functions
func _process(_delta: float) -> void:
	# Update status display periodically when in debug mode
	if debug_mode_checkbox and debug_mode_checkbox.button_pressed:
		_update_status_display()

func get_ai_debug_info() -> Dictionary:
	if not GameState or not GameState.ai_manager:
		return {}
	
	var debug_info = {}
	var ai_manager = GameState.ai_manager
	
	# Captain debug info
	if ai_manager.ai_captain:
		debug_info["captain"] = {
			"difficulty": ai_manager.ai_captain.difficulty,
			"is_done": ai_manager.ai_captain.is_done(),
			"enemy_position": ai_manager.ai_captain.enemy_position,
			"enemy_certainty": ai_manager.ai_captain.enemy_certainty
		}
	
	# Engineer debug info
	if ai_manager.ai_engineer:
		debug_info["engineer"] = {
			"difficulty": ai_manager.ai_engineer.difficulty,
			"is_done": ai_manager.ai_engineer.is_done(),
			"should_surface": ai_manager.ai_engineer.should_surface,
			"damage_status": ai_manager.ai_engineer.get_damage_status()
		}
	
	# First Mate debug info
	if ai_manager.ai_first_mate:
		debug_info["first_mate"] = {
			"difficulty": ai_manager.ai_first_mate.difficulty,
			"is_done": ai_manager.ai_first_mate.is_done(),
			"equipment_status": ai_manager.ai_first_mate.get_equipment_status(),
			"charging_priority": ai_manager.ai_first_mate.get_charging_priority()
		}
	
	# Radio Operator debug info
	if ai_manager.ai_radio_operator:
		debug_info["radio_operator"] = {
			"difficulty": ai_manager.ai_radio_operator.difficulty,
			"is_done": ai_manager.ai_radio_operator.is_done(),
			"certainty": ai_manager.ai_radio_operator.get_certainty(),
			"estimated_position": ai_manager.ai_radio_operator.get_estimated_position()
		}
	
	return debug_info

# Preset configurations
func _on_easy_preset_pressed() -> void:
	_set_all_difficulties(AIProfile.Difficulty.EASY)

func _on_normal_preset_pressed() -> void:
	_set_all_difficulties(AIProfile.Difficulty.NORMAL)

func _on_hard_preset_pressed() -> void:
	_set_all_difficulties(AIProfile.Difficulty.HARD)

func _on_mixed_preset_pressed() -> void:
	# Set a mixed difficulty configuration
	if captain_difficulty_option:
		captain_difficulty_option.selected = AIProfile.Difficulty.NORMAL - 1
	if engineer_difficulty_option:
		engineer_difficulty_option.selected = AIProfile.Difficulty.EASY - 1
	if first_mate_difficulty_option:
		first_mate_difficulty_option.selected = AIProfile.Difficulty.NORMAL - 1
	if radio_difficulty_option:
		radio_difficulty_option.selected = AIProfile.Difficulty.HARD - 1

func _set_all_difficulties(difficulty: AIProfile.Difficulty) -> void:
	var index = difficulty - 1
	if captain_difficulty_option:
		captain_difficulty_option.selected = index
	if engineer_difficulty_option:
		engineer_difficulty_option.selected = index
	if first_mate_difficulty_option:
		first_mate_difficulty_option.selected = index
	if radio_difficulty_option:
		radio_difficulty_option.selected = index
