[gd_scene load_steps=2 format=3 uid="uid://bqm8xvn7qkqxr"]

[ext_resource type="Script" path="res://AIConfig.gd" id="1_8k2vx"]

[node name="AIConfig" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_8k2vx")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -300.0
offset_right = 200.0
offset_bottom = 300.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "AI Configuration"
horizontal_alignment = 1

[node name="AIEnabledCheckbox" type="CheckBox" parent="VBoxContainer"]
layout_mode = 2
button_pressed = true
text = "Enable AI"

[node name="DebugModeCheckbox" type="CheckBox" parent="VBoxContainer"]
layout_mode = 2
text = "Debug Mode"

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="CaptainPanel" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="CaptainLabel" type="Label" parent="VBoxContainer/CaptainPanel"]
layout_mode = 2
text = "Captain Difficulty:"

[node name="CaptainDifficulty" type="OptionButton" parent="VBoxContainer/CaptainPanel"]
layout_mode = 2

[node name="EngineerPanel" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="EngineerLabel" type="Label" parent="VBoxContainer/EngineerPanel"]
layout_mode = 2
text = "Engineer Difficulty:"

[node name="EngineerDifficulty" type="OptionButton" parent="VBoxContainer/EngineerPanel"]
layout_mode = 2

[node name="FirstMatePanel" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="FirstMateLabel" type="Label" parent="VBoxContainer/FirstMatePanel"]
layout_mode = 2
text = "First Mate Difficulty:"

[node name="FirstMateDifficulty" type="OptionButton" parent="VBoxContainer/FirstMatePanel"]
layout_mode = 2

[node name="RadioPanel" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="RadioLabel" type="Label" parent="VBoxContainer/RadioPanel"]
layout_mode = 2
text = "Radio Op Difficulty:"

[node name="RadioDifficulty" type="OptionButton" parent="VBoxContainer/RadioPanel"]
layout_mode = 2

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="PresetPanel" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="EasyPresetButton" type="Button" parent="VBoxContainer/PresetPanel"]
layout_mode = 2
text = "Easy"

[node name="NormalPresetButton" type="Button" parent="VBoxContainer/PresetPanel"]
layout_mode = 2
text = "Normal"

[node name="HardPresetButton" type="Button" parent="VBoxContainer/PresetPanel"]
layout_mode = 2
text = "Hard"

[node name="MixedPresetButton" type="Button" parent="VBoxContainer/PresetPanel"]
layout_mode = 2
text = "Mixed"

[node name="HSeparator3" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "AI Status: Not Active"
vertical_alignment = 1

[node name="HSeparator4" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="TestButtonsPanel" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="TestLabel" type="Label" parent="VBoxContainer/TestButtonsPanel"]
layout_mode = 2
text = "Test Functions:"
horizontal_alignment = 1

[node name="TestButtonsRow1" type="HBoxContainer" parent="VBoxContainer/TestButtonsPanel"]
layout_mode = 2

[node name="TestMovementButton" type="Button" parent="VBoxContainer/TestButtonsPanel/TestButtonsRow1"]
layout_mode = 2
text = "Test Movement"

[node name="TestSurfaceButton" type="Button" parent="VBoxContainer/TestButtonsPanel/TestButtonsRow1"]
layout_mode = 2
text = "Test Surface"

[node name="TestButtonsRow2" type="HBoxContainer" parent="VBoxContainer/TestButtonsPanel"]
layout_mode = 2

[node name="TestEquipmentButton" type="Button" parent="VBoxContainer/TestButtonsPanel/TestButtonsRow2"]
layout_mode = 2
text = "Test Equipment"

[node name="ResetAIButton" type="Button" parent="VBoxContainer/TestButtonsPanel/TestButtonsRow2"]
layout_mode = 2
text = "Reset AI"

[node name="HSeparator5" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ButtonPanel" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="ApplyButton" type="Button" parent="VBoxContainer/ButtonPanel"]
layout_mode = 2
text = "Apply Settings"

[node name="BackButton" type="Button" parent="VBoxContainer/ButtonPanel"]
layout_mode = 2
text = "Back to Menu"
